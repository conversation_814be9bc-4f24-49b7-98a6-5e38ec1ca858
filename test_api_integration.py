#!/usr/bin/env python3
"""
Test script for Jnana API integration with real LLM providers.

This script tests that Jnana can successfully make API calls to OpenAI and Anthropic
using the configured API keys.
"""

import asyncio
import logging
from pathlib import Path

from jnana import JnanaSystem
from jnana.utils import setup_logging


async def test_openai_integration():
    """Test OpenAI API integration."""
    print("🧪 Testing OpenAI API integration...")
    
    try:
        # Test with OpenAI directly
        import openai
        
        # Get API key from config
        from jnana.core.model_manager import UnifiedModelManager
        manager = UnifiedModelManager("config/models.yaml")
        openai_config = manager.get_task_model("hypothesis_generation")
        
        client = openai.OpenAI(api_key=openai_config.api_key)
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a scientific hypothesis generator."},
                {"role": "user", "content": "Generate a brief hypothesis about renewable energy storage. Keep it under 100 words."}
            ],
            max_tokens=150,
            temperature=0.7
        )
        
        hypothesis_text = response.choices[0].message.content
        print(f"✅ OpenAI API working! Generated hypothesis:")
        print(f"   {hypothesis_text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API test failed: {e}")
        return False


async def test_anthropic_integration():
    """Test Anthropic API integration."""
    print("🧪 Testing Anthropic API integration...")
    
    try:
        # Test with Anthropic directly
        import anthropic
        
        # Get API key from config
        from jnana.core.model_manager import UnifiedModelManager
        manager = UnifiedModelManager("config/models.yaml")
        anthropic_config = manager.get_task_model("hypothesis_refinement")
        
        client = anthropic.Anthropic(api_key=anthropic_config.api_key)
        
        response = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=150,
            temperature=0.6,
            messages=[
                {"role": "user", "content": "Generate a brief scientific hypothesis about climate change mitigation. Keep it under 100 words."}
            ]
        )
        
        hypothesis_text = response.content[0].text
        print(f"✅ Anthropic API working! Generated hypothesis:")
        print(f"   {hypothesis_text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Anthropic API test failed: {e}")
        return False


async def test_jnana_with_real_apis():
    """Test Jnana system with real API calls."""
    print("🧪 Testing Jnana system with real API calls...")
    
    try:
        # Initialize Jnana with real API configuration
        jnana = JnanaSystem(
            config_path="config/models.yaml",
            storage_path="test_api_data.json",
            storage_type="json",
            enable_ui=False
        )
        
        await jnana.start()
        print("✅ Jnana system started with real API configuration")
        
        # Set a research goal
        research_goal = "How can we develop more efficient solar energy storage systems using novel battery technologies?"
        session_id = await jnana.set_research_goal(research_goal)
        print(f"✅ Research goal set: {session_id[:8]}...")
        
        # Test hypothesis generation (this will make a real API call)
        print("🔄 Generating hypothesis with real API call...")
        hypothesis = await jnana.generate_single_hypothesis("literature_exploration")
        
        print(f"✅ Real hypothesis generated!")
        print(f"   Title: {hypothesis.title}")
        print(f"   Content: {hypothesis.content[:150]}...")
        
        # Test hypothesis refinement (another real API call)
        print("🔄 Refining hypothesis with real API call...")
        feedback = "Please make this hypothesis more specific to lithium-ion battery improvements and include potential efficiency metrics."
        
        refined_hypothesis = await jnana.refine_hypothesis(hypothesis, feedback)
        
        print(f"✅ Real hypothesis refinement completed!")
        print(f"   Version: {refined_hypothesis.version_string}")
        print(f"   Refined content: {refined_hypothesis.content[:150]}...")
        
        # Get system status
        status = jnana.get_system_status()
        print(f"✅ System status: {status['session']['hypotheses_count']} hypotheses generated")
        
        await jnana.stop()
        print("✅ Jnana system stopped cleanly")
        
        return True
        
    except Exception as e:
        print(f"❌ Jnana API integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_configuration_validation():
    """Test that the configuration is properly loaded and validated."""
    print("🧪 Testing configuration validation...")
    
    try:
        from jnana.core.model_manager import UnifiedModelManager
        
        manager = UnifiedModelManager("config/models.yaml")
        
        # Test validation
        errors = manager.validate_configuration()
        
        if errors:
            print(f"⚠️  Configuration has {len(errors)} warnings:")
            for error in errors:
                print(f"    - {error}")
        else:
            print("✅ Configuration validation passed - all API keys properly configured")
        
        # Test model access
        models = manager.list_available_models()
        print(f"✅ Configuration loaded: {sum(len(m) for m in models.values())} total model configurations")
        
        # Test specific model configs
        openai_model = manager.get_task_model("hypothesis_generation")
        anthropic_model = manager.get_task_model("hypothesis_refinement")
        
        print(f"✅ OpenAI model: {openai_model.provider}:{openai_model.model}")
        print(f"✅ Anthropic model: {anthropic_model.provider}:{anthropic_model.model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


async def main():
    """Run all API integration tests."""
    print("🚀 Starting Jnana API Integration Tests")
    print("=" * 60)
    
    # Setup logging
    setup_logging(level="INFO")
    
    test_results = []
    
    # Test 1: Configuration validation
    print("\n1. Configuration Validation")
    print("-" * 30)
    result1 = await test_configuration_validation()
    test_results.append(("Configuration", result1))
    
    # Test 2: Direct OpenAI API test
    print("\n2. Direct OpenAI API Test")
    print("-" * 30)
    result2 = await test_openai_integration()
    test_results.append(("OpenAI Direct", result2))
    
    # Test 3: Direct Anthropic API test
    print("\n3. Direct Anthropic API Test")
    print("-" * 30)
    result3 = await test_anthropic_integration()
    test_results.append(("Anthropic Direct", result3))
    
    # Test 4: Full Jnana integration test
    print("\n4. Full Jnana Integration Test")
    print("-" * 30)
    result4 = await test_jnana_with_real_apis()
    test_results.append(("Jnana Integration", result4))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 API Integration Test Results")
    print("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All API integration tests passed! Jnana is ready for real-world use.")
    else:
        print("⚠️  Some tests failed. Please check API keys and network connectivity.")
    
    return passed == len(test_results)


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
