"""
Core framework for the AI Co-scientist agent system.
Defines base agent classes and the supervisor system.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
import uuid
import json
import time
import asyncio
from queue import Queue, PriorityQueue
import threading
import logging
import os

from llm_interface import LLMInterface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ResearchHypothesis:
    """Represents a research hypothesis generated by the system."""
    
    def __init__(self, 
                 content: str, 
                 summary: str, 
                 agent_id: str,
                 hypothesis_id: Optional[str] = None,
                 elo_rating: float = 1200.0,
                 metadata: Optional[Dict] = None):
        """
        Initialize a research hypothesis.
        
        Args:
            content: The full text of the hypothesis
            summary: A brief summary of the hypothesis
            agent_id: ID of the agent that generated this hypothesis
            hypothesis_id: Optional ID for the hypothesis (auto-generated if not provided)
            elo_rating: Initial Elo rating (default: 1200)
            metadata: Optional additional information about the hypothesis
        """
        self.hypothesis_id = hypothesis_id or str(uuid.uuid4())
        self.content = content
        self.summary = summary
        self.agent_id = agent_id
        self.created_at = time.time()
        self.elo_rating = elo_rating
        self.metadata = metadata or {}
        self.reviews = []
        self.tournament_matches = []
    
    def add_review(self, review: Dict):
        """Add a review to this hypothesis."""
        self.reviews.append(review)
    
    def add_tournament_match(self, match_result: Dict):
        """Add a tournament match result to this hypothesis."""
        self.tournament_matches.append(match_result)
    
    def to_dict(self) -> Dict:
        """Convert the hypothesis to a dictionary."""
        return {
            "hypothesis_id": self.hypothesis_id,
            "content": self.content,
            "summary": self.summary,
            "agent_id": self.agent_id,
            "created_at": self.created_at,
            "elo_rating": self.elo_rating,
            "metadata": self.metadata,
            "reviews": self.reviews,
            "tournament_matches": self.tournament_matches
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ResearchHypothesis':
        """Create a hypothesis from a dictionary."""
        hypothesis = cls(
            content=data["content"],
            summary=data["summary"],
            agent_id=data["agent_id"],
            hypothesis_id=data["hypothesis_id"],
            elo_rating=data["elo_rating"],
            metadata=data["metadata"]
        )
        hypothesis.created_at = data["created_at"]
        hypothesis.reviews = data["reviews"]
        hypothesis.tournament_matches = data["tournament_matches"]
        return hypothesis
    
    def __str__(self) -> str:
        return f"Hypothesis {self.hypothesis_id} - {self.summary} (Elo: {self.elo_rating:.1f})"


class ContextMemory:
    """Persistent memory to store the state of the co-scientist system."""
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        Initialize the context memory.
        
        Args:
            storage_path: Optional path to a JSON file for persistence
        """
        self.storage_path = storage_path
        self.hypotheses = {}  # Map of hypothesis_id to ResearchHypothesis
        self.experiments = {}  # Map of experiment_id to Experiment
        self.analyses = {}  # Map of analysis_id to Analysis
        self.papers = {}  # Map of paper_id to Paper
        self.metadata = {}  # General metadata about the research
        self.agent_states = {}  # Map of agent_id to agent state
        self.datasets = {}  # Map of task_id to dataset
        self.training_plans = {}  # Map of task_id to training plan
        self.evaluations = {}  # Map of task_id to evaluation
        self.tournament_state = {"matches": []}  # Tournament state
        
        # Load from storage if provided
        if storage_path and os.path.exists(storage_path):
            self.load()
    
    def load(self):
        """Load memory from storage."""
        try:
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
            
            # Load hypotheses
            self.hypotheses = {}
            for h_data in data.get('hypotheses', []):
                hypothesis = ResearchHypothesis.from_dict(h_data)
                self.hypotheses[hypothesis.hypothesis_id] = hypothesis
            
            # Load experiments
            self.experiments = data.get('experiments', {})
            
            # Load analyses
            self.analyses = data.get('analyses', {})
            
            # Load papers
            self.papers = data.get('papers', {})
            
            # Load metadata
            self.metadata = data.get('metadata', {})
            
            # Load agent states
            self.agent_states = data.get('agent_states', {})
            
            # Load datasets
            self.datasets = data.get('datasets', {})
            
            # Load training plans
            self.training_plans = data.get('training_plans', {})
            
            # Load evaluations
            self.evaluations = data.get('evaluations', {})
            
            # Load tournament state
            self.tournament_state = data.get('tournament_state', {"matches": []})
            
            logging.info(f"Loaded memory from {self.storage_path} with {len(self.hypotheses)} hypotheses")
        except Exception as e:
            logging.error(f"Error loading memory from {self.storage_path}: {str(e)}")
    
    def save(self):
        """Save memory to storage."""
        if not self.storage_path:
            return
        
        try:
            # Convert hypotheses to dictionaries
            hypotheses_data = [h.to_dict() for h in self.hypotheses.values()]
            
            # Prepare data for saving
            data = {
                'hypotheses': hypotheses_data,
                'experiments': self.experiments,
                'analyses': self.analyses,
                'papers': self.papers,
                'metadata': self.metadata,
                'agent_states': self.agent_states,
                'datasets': self.datasets,
                'training_plans': self.training_plans,
                'evaluations': self.evaluations,
                'tournament_state': self.tournament_state
            }
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(self.storage_path)), exist_ok=True)
            
            # Save to file
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logging.info(f"Saved memory to {self.storage_path}")
        except Exception as e:
            logging.error(f"Error saving memory to {self.storage_path}: {str(e)}")
    
    def _save_if_needed(self):
        """Save memory if storage path is provided."""
        if self.storage_path:
            self.save()
    
    def get_top_hypotheses(self, k: int = 10) -> List[ResearchHypothesis]:
        """
        Get the top-k hypotheses by Elo rating.
        
        Args:
            k: Number of top hypotheses to return
            
        Returns:
            List of top hypotheses sorted by Elo rating
        """
        # Get all hypotheses
        hypotheses = list(self.hypotheses.values())
        
        # Sort by Elo rating
        top_hypotheses = sorted(hypotheses, key=lambda h: h.elo_rating, reverse=True)[:k]
        
        return top_hypotheses
    
    def add_hypothesis(self, hypothesis: ResearchHypothesis) -> None:
        """
        Add a hypothesis to memory.
        
        Args:
            hypothesis: The hypothesis to add
        """
        self.hypotheses[hypothesis.hypothesis_id] = hypothesis
        self._save_if_needed()
    
    def get_hypothesis(self, hypothesis_id: str) -> Optional[ResearchHypothesis]:
        """
        Get a hypothesis by ID.
        
        Args:
            hypothesis_id: The ID of the hypothesis to retrieve
            
        Returns:
            The hypothesis if found, None otherwise
        """
        return self.hypotheses.get(hypothesis_id)
    
    def get_all_hypotheses(self) -> List[ResearchHypothesis]:
        """
        Get all hypotheses.
        
        Returns:
            List of all hypotheses
        """
        return list(self.hypotheses.values())
    
    def update_hypothesis(self, hypothesis: ResearchHypothesis) -> None:
        """
        Update a hypothesis in memory.
        
        Args:
            hypothesis: The hypothesis to update
        """
        self.hypotheses[hypothesis.hypothesis_id] = hypothesis
        self._save_if_needed()
    
    def get_agent_state(self, agent_id: str) -> Optional[Dict]:
        """
        Get the state of an agent.
        
        Args:
            agent_id: The ID of the agent
            
        Returns:
            The agent state if found, None otherwise
        """
        return self.agent_states.get(agent_id)
    
    def set_agent_state(self, agent_id: str, state: Dict) -> None:
        """
        Set the state of an agent.
        
        Args:
            agent_id: The ID of the agent
            state: The state to set
        """
        self.agent_states[agent_id] = state
        self._save_if_needed()
    
    def update_statistics(self, statistics: Dict) -> None:
        """
        Update system statistics.
        
        Args:
            statistics: The statistics to update
        """
        self.metadata['statistics'] = statistics
        self._save_if_needed()
    
    def add_paper(self, paper: Dict) -> None:
        """
        Add a paper to memory.
        
        Args:
            paper: The paper to add
        """
        self.papers[paper['paper_id']] = paper
        self._save_if_needed()
    
    def set_research_goal(self, research_goal: str, research_plan: Dict) -> None:
        """
        Set the research goal and plan.
        
        Args:
            research_goal: The research goal
            research_plan: The parsed research plan
        """
        self.metadata["research_goal"] = research_goal
        self.metadata["research_plan"] = research_plan
        self._save_if_needed()
    
    def add_experiment(self, experiment: Dict) -> None:
        """
        Add an experiment to memory.
        
        Args:
            experiment: The experiment to add
        """
        self.experiments[experiment['experiment_id']] = experiment
        self._save_if_needed()
    
    def add_analysis(self, analysis: Dict) -> None:
        """
        Add an analysis to memory.
        
        Args:
            analysis: The analysis to add
        """
        self.analyses[analysis['analysis_id']] = analysis
        self._save_if_needed()
    
    def set_dataset(self, task_id: str, dataset: Dict) -> None:
        """
        Set a dataset for a task.
        
        Args:
            task_id: The ID of the task
            dataset: The dataset
        """
        self.datasets[task_id] = dataset
        self._save_if_needed()
    
    def get_dataset(self, task_id: str) -> Optional[Dict]:
        """
        Get a dataset for a task.
        
        Args:
            task_id: The ID of the task
            
        Returns:
            The dataset if found, None otherwise
        """
        return self.datasets.get(task_id)
    
    def set_training_plan(self, task_id: str, training_plan: Dict) -> None:
        """
        Set a training plan for a task.
        
        Args:
            task_id: The ID of the task
            training_plan: The training plan
        """
        self.training_plans[task_id] = training_plan
        self._save_if_needed()
    
    def get_training_plan(self, task_id: str) -> Optional[Dict]:
        """
        Get a training plan for a task.
        
        Args:
            task_id: The ID of the task
            
        Returns:
            The training plan if found, None otherwise
        """
        return self.training_plans.get(task_id)
    
    def set_evaluation(self, task_id: str, evaluation: Dict) -> None:
        """
        Set an evaluation for a task.
        
        Args:
            task_id: The ID of the task
            evaluation: The evaluation
        """
        self.evaluations[task_id] = evaluation
        self._save_if_needed()
    
    def get_evaluation(self, task_id: str) -> Optional[Dict]:
        """
        Get an evaluation for a task.
        
        Args:
            task_id: The ID of the task
            
        Returns:
            The evaluation if found, None otherwise
        """
        return self.evaluations.get(task_id)
    
    def add_tournament_match(self, match: Dict) -> None:
        """
        Add a tournament match to memory.
        
        Args:
            match: The match to add
        """
        self.tournament_state["matches"].append(match)
        self._save_if_needed()
    
    def get_tournament_matches(self) -> List[Dict]:
        """
        Get all tournament matches.
        
        Returns:
            List of all tournament matches
        """
        return self.tournament_state["matches"]


class Task:
    """Represents a task to be executed by an agent."""
    
    def __init__(self, 
                 task_type: str, 
                 agent_type: str,
                 priority: int = 1,
                 params: Optional[Dict] = None,
                 task_id: Optional[str] = None):
        """
        Initialize a task.
        
        Args:
            task_type: Type of task (e.g., "generate_hypothesis", "review")
            agent_type: Type of agent that should execute this task
            priority: Task priority (lower number = higher priority)
            params: Optional parameters for the task
            task_id: Optional ID for the task (auto-generated if not provided)
        """
        self.task_id = task_id or str(uuid.uuid4())
        self.task_type = task_type
        self.agent_type = agent_type
        self.priority = priority
        self.params = params or {}
        self.created_at = time.time()
        self.status = "pending"  # pending, running, completed, failed
        self.result = None
        self.error = None
    
    def to_dict(self) -> Dict:
        """Convert the task to a dictionary."""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "agent_type": self.agent_type,
            "priority": self.priority,
            "params": self.params,
            "created_at": self.created_at,
            "status": self.status,
            "result": self.result,
            "error": self.error
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Task':
        """Create a task from a dictionary."""
        task = cls(
            task_type=data["task_type"],
            agent_type=data["agent_type"],
            priority=data["priority"],
            params=data["params"],
            task_id=data["task_id"]
        )
        task.created_at = data["created_at"]
        task.status = data["status"]
        task.result = data["result"]
        task.error = data["error"]
        return task
    
    def __lt__(self, other):
        """Compare tasks by priority for the priority queue."""
        if self.priority != other.priority:
            return self.priority < other.priority
        return self.created_at < other.created_at


class Agent(ABC):
    """Base class for all agents in the co-scientist system."""
    
    def __init__(self, 
                 agent_id: str,
                 agent_type: str,
                 llm: LLMInterface,
                 memory: ContextMemory):
        """
        Initialize an agent.
        
        Args:
            agent_id: Unique identifier for this agent instance
            agent_type: Type of agent (e.g., "generation", "reflection")
            llm: LLM interface to use for generation
            memory: Shared context memory
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.llm = llm
        self.memory = memory
        self.logger = logging.getLogger(f"Agent-{agent_type}-{agent_id}")
        self.total_calls = 0
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
    
    @abstractmethod
    async def execute_task(self, task: Task) -> Dict:
        """
        Execute a task and return the result.
        
        Args:
            task: The task to execute
            
        Returns:
            Dictionary containing the task result
        """
        pass
    
    def get_prompt_template(self, template_name: str) -> str:
        """
        Get a prompt template for this agent.
        
        Args:
            template_name: Name of the template to retrieve
            
        Returns:
            The prompt template as a string
        """
        # In a real implementation, these would be loaded from files or a database
        # For now, we'll use placeholder templates
        templates = {
            "default": "You are an AI co-scientist agent. Your role is to {role}.\n\nResearch goal: {research_goal}\n\nTask: {task}\n\n",
            "system": "You are an AI co-scientist {agent_type} agent. Your role is to {role}."
        }
        
        return templates.get(template_name, templates["default"])
    
    def fill_prompt_template(self, template_name: str, **kwargs) -> str:
        """
        Fill a prompt template with variables.
        
        Args:
            template_name: Name of the template to fill
            **kwargs: Variables to fill in the template
            
        Returns:
            The filled prompt template
        """
        template = self.get_prompt_template(template_name)
        return template.format(**kwargs)
    
    def get_usages(self):
        return self.total_calls, self.total_prompt_tokens, self.total_completion_tokens


class SupervisorAgent:
    """
    Manages the overall co-scientist system, assigns tasks, and monitors progress.
    """
    
    def __init__(self, 
                 llm: LLMInterface,
                 memory: ContextMemory,
                 max_workers: int = 4):
        """
        Initialize the supervisor agent.
        
        Args:
            llm: LLM interface to use
            memory: Shared context memory
            max_workers: Maximum number of worker threads to use
        """
        self.agent_id = "supervisor"
        self.llm = llm
        self.memory = memory
        self.max_workers = max_workers
        self.logger = logging.getLogger("SupervisorAgent")
        self.total_calls = 0
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
        
        # Task queue and worker threads
        self.task_queue = PriorityQueue()
        self.workers = []
        self.agents = {}  # agent_id -> Agent instance
        self.agent_types = {}  # agent_type -> [agent_id]
        self.is_running = False
        self.stop_event = threading.Event()
    
    def register_agent(self, agent: Agent):
        """
        Register an agent with the supervisor.
        
        Args:
            agent: The agent to register
        """
        self.agents[agent.agent_id] = agent
        if agent.agent_type not in self.agent_types:
            self.agent_types[agent.agent_type] = []
        self.agent_types[agent.agent_type].append(agent.agent_id)
        self.logger.info(f"Registered agent {agent.agent_id} of type {agent.agent_type}")
    
    def get_usages(self, verbose=True):
        total_calls = {"supervisor": self.total_calls}
        total_prompt_tokens = {"supervisor": self.total_prompt_tokens}
        total_completion_tokens = {"supervisor": self.total_completion_tokens}

        for agent_id, agent in self.agents.items():
            total_calls[agent_id] = agent.total_calls
            total_prompt_tokens[agent_id] = agent.total_prompt_tokens
            total_completion_tokens[agent_id] = agent.total_completion_tokens

        result_payload = {
            "total_calls": total_calls,
            "total_prompt_tokens": total_prompt_tokens,
            "total_completion_tokens": total_completion_tokens
        }

        with open("./model_usages.json", "w") as f:
            json.dump(result_payload, f)

        if verbose:
            self.logger.info("Total model usages:")
            self.logger.info(result_payload)
        
        return result_payload

    def add_task(self, task: Task):
        """
        Add a task to the queue.
        
        Args:
            task: The task to add
        """
        self.task_queue.put(task)
        self.logger.info(f"Added task {task.task_id} of type {task.task_type} to queue")
    
    def worker_function(self, worker_id: int):
        """
        Worker thread function that processes tasks from the queue.
        
        Args:
            worker_id: ID of this worker thread
        """
        worker_logger = logging.getLogger(f"Worker-{worker_id}")
        worker_logger.info(f"Worker {worker_id} started")
        
        while not self.stop_event.is_set():
            try:
                # Get task from queue with a timeout to allow checking the stop event
                try:
                    task = self.task_queue.get(timeout=1)
                except Exception:
                    # Queue is empty or timed out, continue the loop
                    continue
                
                worker_logger.info(f"Processing task {task.task_id} of type {task.task_type}")
                
                # Update task status
                task.status = "running"
                
                # Select an agent of the appropriate type
                if task.agent_type not in self.agent_types or not self.agent_types[task.agent_type]:
                    task.status = "failed"
                    task.error = f"No agents of type {task.agent_type} registered"
                    worker_logger.error(task.error)
                    continue
                
                # Simple round-robin selection among agents of the same type
                agent_ids = self.agent_types[task.agent_type]
                agent_id = agent_ids[worker_id % len(agent_ids)]
                agent = self.agents[agent_id]
                
                try:
                    # Execute the task
                    result = asyncio.run(agent.execute_task(task))
                    task.result = result
                    task.status = "completed"
                    worker_logger.info(f"Task {task.task_id} completed")
                except Exception as e:
                    task.status = "failed"
                    task.error = str(e)
                    worker_logger.error(f"Task {task.task_id} failed: {str(e)}")
                
                # Mark the task as done in the queue
                self.task_queue.task_done()
                
            except Exception as e:
                worker_logger.error(f"Worker {worker_id} encountered an error: {str(e)}")
        
        worker_logger.info(f"Worker {worker_id} stopped")
    
    def start(self):
        """Start the supervisor and worker threads."""
        if self.is_running:
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # Start worker threads
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker_function, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        self.logger.info(f"Supervisor started with {self.max_workers} workers")
    
    def stop(self):
        """Stop the supervisor and worker threads."""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        # Wait for all workers to finish
        for worker in self.workers:
            worker.join(timeout=5)
        
        self.workers = []
        self.logger.info("Supervisor stopped")
    
    def wait_for_all_tasks(self, timeout: Optional[float] = None):
        """
        Wait for all tasks in the queue to be processed.
        
        Args:
            timeout: Optional timeout in seconds
        """
        self.task_queue.join()
        self.logger.info("All tasks completed")
    
    def calculate_statistics(self) -> Dict:
        """
        Calculate statistics about the system state.
        
        Returns:
            Dictionary of statistics
        """
        hypotheses = self.memory.get_all_hypotheses()
        
        # Sort hypotheses by Elo rating
        ranked_hypotheses = sorted(hypotheses, key=lambda h: h.elo_rating, reverse=True)
        
        statistics = {
            "total_hypotheses": len(hypotheses),
            "top_10_hypotheses": [h.hypothesis_id for h in ranked_hypotheses[:10]],
            "average_elo": sum(h.elo_rating for h in hypotheses) / max(1, len(hypotheses)),
            "completed_tournament_matches": len(self.memory.tournament_state["matches"]),
            "timestamp": time.time()
        }
        
        # Update memory with statistics
        self.memory.update_statistics(statistics)
        
        return statistics
    
    def parse_research_goal(self, research_goal: str) -> Dict:
        """
        Parse the research goal into a structured plan.
        
        Args:
            research_goal: The research goal specified by the scientist
        
        Returns:
            Dictionary containing the parsed research plan configuration
        """
        self.logger.info(f"Parsing research goal: {research_goal[:100]}...")
        
        # Create a schema for the expected output
        schema = {
            "type": "object",
            "properties": {
                "main_objective": {"type": "string"},
                "domain": {"type": "string"},
                "constraints": {"type": "array", "items": {"type": "string"}},
                "preferences": {"type": "array", "items": {"type": "string"}},
                "evaluation_criteria": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["main_objective", "domain", "evaluation_criteria"]
        }
        
        # Create a prompt for the LLM
        prompt = f"""
        You are a scientific research planner. Your task is to analyze the following research goal
        and extract key information to guide a research process.

        Research Goal:
        {research_goal}

        Please extract:
        1. The main objective of the research
        2. The scientific domain(s) involved
        3. Any constraints or limitations
        4. Any preferences or priorities
        5. Criteria that should be used to evaluate hypotheses

        Format your response as a structured JSON object.
        """

        try:
            # Generate the research plan
            config = self.llm.generate_with_json_output(prompt, schema)
            
            # Add the original research goal to the config
            config["original_research_goal"] = research_goal
            
            return config
        except Exception as e:
            self.logger.error(f"Error parsing research goal: {str(e)}")
            # Fallback to a simple configuration
            return {
                "main_objective": research_goal,
                "domain": "unknown",
                "constraints": [],
                "preferences": [],
                "evaluation_criteria": ["novelty", "plausibility", "testability"],
                "original_research_goal": research_goal
            }
