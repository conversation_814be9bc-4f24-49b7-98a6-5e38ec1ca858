# ProtoGnosis: AI Co-Scientist with Local LLM Integration

ProtoGnosis is an AI-powered research assistant that helps scientists generate, evaluate, and refine research hypotheses. It uses a multi-agent system with different specialized agents working together to explore research questions.

## Key Features

- **Multi-Agent System**: Different specialized agents for generation, reflection, ranking, and more
- **Multiple LLM Providers**: Support for various LLM providers including:
  - Cloud-based: OpenAI, Anthropic Claude, Google Gemini
  - Local: Ollama, LLM Studio
- **Flexible Configuration**: Mix and match different LLM providers for different agent types
- **Hypothesis Generation**: Generate diverse research hypotheses using various strategies
- **Evaluation & Ranking**: Evaluate and rank hypotheses through tournaments
- **Research Insights**: Generate meta-reviews and research overviews

## Local LLM Integration

ProtoGnosis now supports local LLMs through:

### Ollama Integration

[Ollama](https://ollama.ai/) provides an easy way to run open-source large language models locally. ProtoGnosis can connect to Ollama to use models like:

- Llama 3
- Mistral
- Mixtral
- Gemma
- And any other model supported by Ollama

### LLM Studio Integration

[LLM Studio](https://github.com/h2oai/h2o-llmstudio) is an IDE for fine-tuning and serving LLMs. ProtoGnosis can connect to LLM Studio to use custom fine-tuned models.

## Getting Started

### Prerequisites

- Python 3.9+
- For local LLMs:
  - [Ollama](https://ollama.ai/) installed and running
  - Or [LLM Studio](https://github.com/h2oai/h2o-llmstudio) installed and running

### Installation

```bash
# Clone the repository
git clone https://github.com/acadev/ProtoGnosis.git
cd ProtoGnosis

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-local-llm.txt  # For local LLM support
```

### Using Local LLMs

To use local LLMs with ProtoGnosis, you need to:

1. Start Ollama or LLM Studio
2. Configure ProtoGnosis to use your local LLM

Example configuration:

```python
from coscientist import CoScientist
from multi_llm_config import LLMConfig, AgentLLMConfig

# Configure with Ollama
llm_config = AgentLLMConfig(
    default=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
    supervisor=LLMConfig(provider="ollama", model="mixtral", base_url="http://localhost:11434"),
)

# Create the co-scientist
coscientist = CoScientist(llm_config=llm_config)

# Set research goal and start
coscientist.set_research_goal("Your research question here")
coscientist.start()

# Generate hypotheses
coscientist.generate_hypotheses(count=3)
```

See `coscientist-example.py` for more detailed examples.

## License

MIT

## Acknowledgments

This project builds on research in multi-agent AI systems and scientific discovery.
