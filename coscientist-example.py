"""
Example usage of the AI Co-scientist with multiple LLM providers.

This example demonstrates how to configure and use the Co-scientist
with different LLM providers for different agent types.
"""
import os
import logging
from pprint import pprint

from coscientist import CoScientist
from multi_llm_config import LLMConfig, AgentLLMConfig

def mixed_llm_example():
    """Example using different LLM providers for different agents."""
    print("Setting up AI Co-scientist with multiple LLM providers...")

    # Create LLM configurations
    # You need to add your API key here
    openai_api_key = "********************************************************************************************************************************************************************"
    anthropic_api_key = "************************************************************************************************************"  # Replace with your actual API key
    # cerebras_api_key = "csk-f2md9etkektpt8pe5ekwtck9me8crncr55tw9wk8fxm6hh5e"
    # cerebras_api_key = "csk-2kn2f45hkdx2wv2dyvm9n5xn2rh5dxdtryv2rt6rejwenfch"
    
    model_name = "llama-4-scout-17b-16e-instruct"

    llm_config = AgentLLMConfig(
        # Default LLM for agents without a specific config
        default=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        #default=LLMConfig(provider="cerebras", model=model_name, api_key=cerebras_api_key),

        # Use Anthropic for the supervisor agent (strategic planning)
        supervisor=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        # supervisor=LLMConfig(provider="cerebras", model=model_name, api_key=cerebras_api_key),

        # Use Claude Opus for generation (creative ideation)
        #generation=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        generation=LLMConfig(provider="openai", model="gpt-4o", api_key=openai_api_key),
        #generation=LLMConfig(provider="cerebras", model=model_name, api_key=cerebras_api_key),

        # Use Claude Opus for reflection (detailed critical review)
        reflection=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        #reflection=LLMConfig(provider="cerebras", model=model_name, api_key=cerebras_api_key),

        # Use Claude for ranking (comparative evaluation)
        ranking=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        #ranking=LLMConfig(provider="cerebras", model=model_name,api_key=cerebras_api_key),

        # Individual agent overrides
        specific_agents={
            # Use a specific config for one of the generation agents
            "generation-1": LLMConfig(provider="anthropic", model="claude-3-5-haiku-20241022", api_key=anthropic_api_key),
            #"generation-1": LLMConfig(provider="cerebras", model=model_name, api_key=cerebras_api_key)
        }
    )

    # Create the co-scientist with our mixed LLM configuration
    coscientist = CoScientist(
        llm_config=llm_config,
        storage_path="./mixed_llm_roboticsLLaMa_openAI.json",
        max_workers=4
    )

    # Define a research goal for drug repurposing
 #   research_goal = """
 #   Suggest a strategy for targeting the intrinsically disordered regions (IDRs) within ALKBH1 for cancer treatment.
 #   I am particularly interested in epigenetics, chromatin remodeling, drug targeting, protein-protein interactions;
 #   specifically focus on drug targets and protein-protein interactions involving ALKBH1. I prefer a mix of both review and primary research;
 #   it would be particularly helpful to put together a comprehensive map of known protein-protein interactions of ALKBH1 in the context of cancer
 #   signaling and how that could be targeted using biologics (consider molecular glues/ PROTACS, small molecules, mini proteins/ nanonodies and antibodies).
#"""

    research_goal = """"
    If your goal is to develop a massively parallel robotics driven 
    science infrastructure for life science research that focuses on 
    massive discovery of protein functions (in bacteria, fungi, plants),
     and the experimental determination of key protein and small molecule 
     interactions to support building virtual cell models, and 
     accelerating genotype to phenotyping across a wide range of 
     laboratory organisms (bacterial, single celled Euks, archea), 
     what would be the top ten open problems to solve in AI, computing, 
     robotics (including humanoid interfacing to fixed robotics), 
     software, data management, protocols. Imagine we are 5 years out 
     from a billion dollar investment and we have $10M to get ready. 
     What are the key problems to solve in the next two years?
    """

    # research_goal = """
    # Can you help me suggest a strategy to develop vaccines for SARS-CoV-2? While most approaches focus on the Spike receptor binding domain (RBD)
    # I would like to get some suggestions on how to target other proteins in the SARS-CoV-2 genome. I am particularly interested in the ORF1ab polyprotein,
    # the nucleocapsid phosphoprotein (N), the membrane glycoprotein (M), and the envelope protein (E). I would like to get some suggestions on how to target
    # these proteins using a combination of review and primary research. I am particularly interested in the immune response to these proteins and how that could
    # be leveraged to develop vaccines. I am also interested in the structural biology of these proteins and how that could inform vaccine design.
    # """
    #    Suggest a strategy for targeting the intrinsically disordered rgions (IDRs) within ALKBH1 for cancer treatment.
    #    The strategy should involve building novel biologics that can target the IDRs within ALKBH1 that interact with
    #   retinoic acid receptor RARα  as well as steroid receptor coactivator SRC-3 (NCOA3) to undergo liquid–liquid phase separation.
    #   The biologics can be peptides, antibodies, or other molecules that can disrupt the interaction between WHSC1 and RARα/SRC-3.
    #   Focus on the novelty and specificity of the biologics, as well as their potential efficacy and safety for cancer treatment.
    #"""

    print("\nSetting research goal...")
    research_plan = coscientist.set_research_goal(research_goal)

    print("\nRunning limited research cycle...")
    # Start the system
    coscientist.start()

    try:
        # Generate initial hypotheses (will use Claude Opus and Gemini)
        # print("Generating initial hypotheses...")
        # coscientist.generate_hypotheses(count=20)
        # coscientist.wait_for_completion()

        # # Review hypotheses (will use Claude Opus)
        # print("Reviewing hypotheses...")
        # coscientist.review_hypotheses()
        # coscientist.wait_for_completion()

        # # Run tournament (will use Gemini)
        # print("Running tournament...")
        # coscientist.run_tournament(match_count=25)
        # coscientist.wait_for_completion()

        # # Get top hypotheses
        # top_hypotheses = coscientist.get_top_hypotheses(5)
        results = coscientist.run_full_cycle(
            iterations=20,
            initial_hypotheses=20,
            matches_per_iteration=20
        )

        print("\nTop 10 candidates:")
        for i, hypothesis in enumerate(results["top_hypotheses"]):
            print(f"\n{i+1}. {hypothesis['summary']} (Elo rating: {hypothesis['elo_rating']:.1f})")
            print(f"   Generated by agent: {hypothesis['agent_id']}")

        print("\nResearch overview:")
        print(f"\n\t{results['research_overview']}")

    finally:
        coscientist.stop()

def alternative_configurations():
    """Demonstrate different ways to configure the CoScientist."""

    # Add your API keys here
    anthropic_api_key = "your_anthropic_api_key_here"  # Replace with your actual API key
    openai_api_key = "your_openai_api_key_here"  # Replace with your actual API key
    cerebras_api_key = "csk-f2md9etkektpt8pe5ekwtck9me8crncr55tw9wk8fxm6hh5e"

    # We'll just print the configurations without creating CoScientist instances
    # to avoid API calls

    print("\n1. Simple string provider:")
    # Simplest form: just specify a provider
    print("Default provider: anthropic")
    print("Default model: claude-3-opus-20240229")

    print("\n2. Single LLMConfig for all agents:")
    # Single LLMConfig for all agents
    llm_config2 = LLMConfig(provider="openai", model="gpt-4o", api_key=cerebras_api_key)
    print(f"Default provider: {llm_config2.provider}")
    print(f"Default model: {llm_config2.model}")

    print("\n3. Different models from the same provider:")
    # Use different models from the same provider
    llm_config3 = AgentLLMConfig(
        default=LLMConfig(provider="anthropic", model="claude-3-5-haiku-20241022", api_key=anthropic_api_key),
        supervisor=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),
        generation=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key)
    )
    print(f"Supervisor model: {llm_config3.supervisor.model}")
    print(f"Generation model: {llm_config3.generation.model}")
    print(f"Default model for other agents: {llm_config3.default.model}")

    print("\n4. Cost-optimized configuration:")
    # Cost-optimized: use cheaper models for most tasks, expensive ones only where needed
    llm_config4 = AgentLLMConfig(
        default=LLMConfig(provider="anthropic", model="claude-3-5-haiku-20241022", api_key=anthropic_api_key),  # Fastest/cheapest
        generation=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key),  # Best for creative tasks
        meta_review=LLMConfig(provider="anthropic", model="claude-3-7-sonnet-20250219", api_key=anthropic_api_key)  # Best for synthesis
    )

    # Print the configurations for the cost-optimized setup
    print(f"Default provider: {llm_config4.default.provider} - {llm_config4.default.model}")
    print(f"Generation provider: {llm_config4.generation.provider} - {llm_config4.generation.model}")
    print(f"Meta-review provider: {llm_config4.meta_review.provider} - {llm_config4.meta_review.model}")

    # For reflection and other agents, they would use the default
    print(f"Reflection would use: {llm_config4.default.provider} - {llm_config4.default.model}")

    print("\n5. Local LLM configuration with Ollama:")
    # Use Ollama for local LLM inference
    llm_config5 = AgentLLMConfig(
        default=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
        # Use a more capable model for the supervisor
        supervisor=LLMConfig(provider="ollama", model="mixtral", base_url="http://localhost:11434"),
    )
    print(f"Default provider: {llm_config5.default.provider} - {llm_config5.default.model}")
    print(f"Supervisor provider: {llm_config5.supervisor.provider} - {llm_config5.supervisor.model}")

    print("\n6. Mixed local and cloud LLM configuration:")
    # Use a mix of local and cloud LLMs
    llm_config6 = AgentLLMConfig(
        # Use local LLM for most tasks
        default=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
        # Use cloud LLM for critical tasks
        supervisor=LLMConfig(provider="openai", model="gpt-4o", api_key=cerebras_api_key),
        generation=LLMConfig(provider="openai", model="gpt-4o", api_key=cerebras_api_key),
    )
    print(f"Default provider: {llm_config6.default.provider} - {llm_config6.default.model}")
    print(f"Supervisor provider: {llm_config6.supervisor.provider} - {llm_config6.supervisor.model}")
    print(f"Generation provider: {llm_config6.generation.provider} - {llm_config6.generation.model}")

    print("\n7. LLM Studio configuration:")
    # Use LLM Studio for local LLM inference
    llm_config7 = AgentLLMConfig(
        default=LLMConfig(provider="llm_studio", base_url="http://localhost:3000"),
    )
    print(f"Default provider: {llm_config7.default.provider} - {llm_config7.default.model}")

def ollama_example():
    """Example using Ollama for local LLM inference."""
    print("Setting up AI Co-scientist with Ollama local LLMs...")

    # Create LLM configurations
    llm_config = AgentLLMConfig(
        # Default LLM for agents without a specific config
        default=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
        
        # Use a more capable model for the supervisor agent (strategic planning)
        supervisor=LLMConfig(provider="ollama", model="mixtral", base_url="http://localhost:11434"),
        
        # Use specific models for different agent types
        generation=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
        reflection=LLMConfig(provider="ollama", model="llama3", base_url="http://localhost:11434"),
        
        # Individual agent overrides
        specific_agents={
            # Use a specific model for one of the generation agents
            "generation-1": LLMConfig(provider="ollama", model="gemma", base_url="http://localhost:11434"),
        }
    )

    # Create the co-scientist with our local LLM configuration
    coscientist = CoScientist(
        llm_config=llm_config,
        storage_path="./ollama_test_results.json",
        max_workers=4
    )

    # Define a simple research goal
    research_goal = """
    Suggest a strategy for targeting the intrinsically disordered regions (IDRs) within ALKBH1 for cancer treatment.
    Focus on drug targets and protein-protein interactions involving ALKBH1.
    """

    print("\nSetting research goal...")
    research_plan = coscientist.set_research_goal(research_goal)

    print("\nRunning limited research cycle...")
    # Start the system
    coscientist.start()

    try:
        # Run a small test cycle
        print("Running a small test cycle...")
        results = coscientist.run_full_cycle(
            iterations=4,
            initial_hypotheses=4,
            matches_per_iteration=2
        )

        print("\nTop 3 candidates:")
        for i, hypothesis in enumerate(results["top_hypotheses"][:3]):
            print(f"\n{i+1}. {hypothesis['summary']} (Elo rating: {hypothesis['elo_rating']:.1f})")
            print(f"   Generated by agent: {hypothesis['agent_id']}")

        print("\nResearch overview:")
        print(f"\n\t{results['research_overview']}")

    finally:
        coscientist.stop()

def cs_reft_example():
    """Example using CS-ReFT adapted models for different agent types."""
    print("Setting up AI Co-scientist with CS-ReFT adapted models...")
    
    # Path to pre-trained CS-ReFT adapter
    adapter_path = "./adapted_models/coscientist_adapter/adapter.pt"
    
    llm_config = AgentLLMConfig(
        # Default LLM for agents without a specific config
        default=LLMConfig(
            provider="local",
            model="llama-2-7b",
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": None  # Use router for multi-task
            }
        ),

        # Use task-specific subspaces for different agent types
        supervisor=LLMConfig(
            provider="local",
            model="llama-2-7b",
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": "supervisor"
            }
        ),

        generation=LLMConfig(
            provider="local",
            model="llama-2-7b",
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": "generation"
            }
        ),

        reflection=LLMConfig(
            provider="local",
            model="llama-2-7b",
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": "reflection"
            }
        ),

        ranking=LLMConfig(
            provider="local",
            model="llama-2-7b",
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": "ranking"
            }
        )
    )

    # Create the co-scientist with our CS-ReFT configuration
    coscientist = CoScientist(
        llm_config=llm_config,
        storage_path="./cs_reft_results.json",
        max_workers=4
    )

    # Define a research goal
    research_goal = """
    Suggest a strategy for targeting the intrinsically disordered regions (IDRs) 
    within ALKBH1 for cancer treatment.
    """

    print("\nSetting research goal...")
    research_plan = coscientist.set_research_goal(research_goal)

    print("\nRunning research cycle with CS-ReFT adapted models...")
    coscientist.start()

    try:
        # Run a small test cycle
        results = coscientist.run_full_cycle(
            iterations=2,
            initial_hypotheses=5,
            matches_per_iteration=5
        )
        
        print("\nTop 3 candidates:")
        for i, hypothesis in enumerate(results["top_hypotheses"][:3]):
            print(f"\n{i+1}. {hypothesis['summary']} (Elo rating: {hypothesis['elo_rating']:.1f})")
            print(f"   Generated by agent: {hypothesis['agent_id']}")
        
        print("\nResearch overview:")
        print(f"\n\t{results['research_overview']}")
        
    finally:
        coscientist.stop()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Run the alternative configurations example instead of the full mixed LLM example
    # This will just test the configuration without making API calls
    # alternative_configurations()

    # Run the local LLM example (requires Ollama to be running)
    # local_llm_example()
    #ollama_example()

    # Run the mixed LLM example (requires API keys)
    mixed_llm_example()

    # Run the CS-ReFT example
    #cs_reft_example()
