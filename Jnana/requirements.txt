# Core dependencies
pyyaml>=6.0
pydantic>=2.0.0
dataclasses-json>=0.6.0
typing-extensions>=4.0.0

# LLM integrations
openai>=1.0.0
anthropic>=0.25.0
google-generativeai>=0.5.0
ollama>=0.1.0

# Async and concurrency
asyncio-mqtt>=0.13.0
aiofiles>=23.0.0
uvloop>=0.19.0

# Data processing
orjson>=3.9.0
pandas>=2.0.0
numpy>=1.24.0

# UI and terminal
rich>=13.0.0
textual>=0.40.0

# PDF generation (from Wisteria)
reportlab>=4.0.0

# Database (optional)
sqlalchemy>=2.0.0
alembic>=1.12.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0

# Development
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0
pre-commit>=3.4.0

# Logging and monitoring
structlog>=23.0.0
prometheus-client>=0.17.0

# Configuration and environment
python-dotenv>=1.0.0
click>=8.1.0

# Performance
cachetools>=5.3.0
redis>=4.6.0
