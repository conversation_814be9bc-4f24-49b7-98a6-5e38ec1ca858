# Jnana Environment Configuration
# Copy this file to .env and fill in your API keys

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Configuration  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Other API Keys
GOOGLE_API_KEY=your_google_api_key_here
COHERE_API_KEY=your_cohere_api_key_here

# Flask Configuration (for web interface)
FLASK_HOST=0.0.0.0
FLASK_PORT=5001
FLASK_DEBUG=False
SECRET_KEY=your_secret_key_for_flask_sessions

# Database Configuration
DATABASE_URL=sqlite:///jnana.db

# Jnana Configuration
JNANA_CONFIG_PATH=config/models.yaml
JNANA_STORAGE_PATH=sessions/
JNANA_LOG_LEVEL=INFO
