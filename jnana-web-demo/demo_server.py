#!/usr/bin/env python3
"""
Simple demo server for Jnana Web Interface.

This serves the HTML demo and provides basic API endpoints to demonstrate
the integration with Jnana core functionality.
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

# Add parent directory to path for Jnana imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

class JnanaDemoHandler(SimpleHTTPRequestHandler):
    """Custom handler for Jnana demo with API endpoints."""
    
    def do_GET(self):
        """Handle GET requests."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            # Serve the main demo page
            self.serve_file('index.html')
        elif parsed_path.path == '/api/health':
            # Health check endpoint
            self.send_json_response({
                'status': 'healthy',
                'service': 'jnana-demo',
                'timestamp': datetime.now().isoformat()
            })
        elif parsed_path.path == '/api/sessions':
            # List sessions endpoint
            self.send_json_response({
                'sessions': [
                    {
                        'session_id': 'demo-session-1',
                        'research_goal': 'Solar Energy Storage Research',
                        'mode': 'interactive',
                        'created_at': '2024-07-09T12:00:00',
                        'hypothesis_count': 3
                    },
                    {
                        'session_id': 'demo-session-2', 
                        'research_goal': 'Climate Change Mitigation Strategies',
                        'mode': 'batch',
                        'created_at': '2024-07-08T15:30:00',
                        'hypothesis_count': 5
                    }
                ]
            })
        elif parsed_path.path.startswith('/api/sessions/') and parsed_path.path.endswith('/hypotheses'):
            # List hypotheses for a session
            self.send_json_response({
                'hypotheses': [
                    {
                        'hypothesis_id': 'demo-hyp-1',
                        'title': 'Perovskite-Silicon Tandem Solar Cells',
                        'description': 'Enhanced efficiency through novel perovskite materials',
                        'version_string': '1.2',
                        'created_at': '2024-07-09T14:00:00',
                        'generation_strategy': 'literature_exploration'
                    },
                    {
                        'hypothesis_id': 'demo-hyp-2',
                        'title': 'Solid-State Battery Integration',
                        'description': 'Lithium-metal anodes with ceramic electrolytes',
                        'version_string': '1.0',
                        'created_at': '2024-07-09T13:00:00',
                        'generation_strategy': 'scientific_debate'
                    }
                ]
            })
        else:
            # Default file serving
            super().do_GET()
    
    def do_POST(self):
        """Handle POST requests."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/sessions':
            # Create session endpoint
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                research_goal = data.get('research_goal', '')
                mode = data.get('mode', 'interactive')
                
                # Simulate session creation
                session = {
                    'session_id': f'demo-session-{datetime.now().strftime("%Y%m%d%H%M%S")}',
                    'research_goal': research_goal,
                    'mode': mode,
                    'status': 'active',
                    'created_at': datetime.now().isoformat(),
                    'hypothesis_count': 0
                }
                
                self.send_json_response(session, status=201)
                
            except json.JSONDecodeError:
                self.send_json_response({'error': 'Invalid JSON'}, status=400)
                
        elif '/hypotheses' in parsed_path.path and not parsed_path.path.endswith('/hypotheses'):
            # Generate or refine hypothesis
            if parsed_path.path.endswith('/refine'):
                # Simulate hypothesis refinement
                self.send_json_response({
                    'hypothesis_id': 'demo-hyp-refined',
                    'title': 'Refined Hypothesis Title',
                    'description': 'This hypothesis has been refined based on your feedback',
                    'version_string': '1.1',
                    'created_at': datetime.now().isoformat()
                })
            else:
                # Simulate hypothesis generation
                self.send_json_response({
                    'hypothesis_id': f'demo-hyp-{datetime.now().strftime("%H%M%S")}',
                    'title': 'AI-Generated Hypothesis',
                    'description': 'This is a newly generated hypothesis using the selected strategy',
                    'version_string': '1.0',
                    'created_at': datetime.now().isoformat(),
                    'generation_strategy': 'literature_exploration'
                }, status=201)
        else:
            self.send_json_response({'error': 'Endpoint not found'}, status=404)
    
    def serve_file(self, filename):
        """Serve a specific file."""
        try:
            with open(filename, 'rb') as f:
                content = f.read()
            
            self.send_response(200)
            if filename.endswith('.html'):
                self.send_header('Content-type', 'text/html')
            elif filename.endswith('.css'):
                self.send_header('Content-type', 'text/css')
            elif filename.endswith('.js'):
                self.send_header('Content-type', 'application/javascript')
            
            self.send_header('Content-length', len(content))
            self.end_headers()
            self.wfile.write(content)
            
        except FileNotFoundError:
            self.send_error(404, f"File not found: {filename}")
    
    def send_json_response(self, data, status=200):
        """Send a JSON response."""
        response = json.dumps(data, indent=2)
        
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Content-length', len(response))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def run_demo_server(port=8080):
    """Run the demo server."""
    server_address = ('', port)
    httpd = HTTPServer(server_address, JnanaDemoHandler)
    
    print(f"🌐 Jnana Web Interface Demo")
    print(f"📱 Open your browser to: http://localhost:{port}")
    print(f"🔧 Backend API available at: http://localhost:{port}/api/")
    print(f"⚡ Press Ctrl+C to stop the server")
    print()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down demo server...")
        httpd.shutdown()

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Jnana Web Interface Demo Server')
    parser.add_argument('--port', type=int, default=8080, help='Port to run the server on (default: 8080)')
    
    args = parser.parse_args()
    
    # Change to the demo directory
    os.chdir(Path(__file__).parent)
    
    run_demo_server(args.port)
