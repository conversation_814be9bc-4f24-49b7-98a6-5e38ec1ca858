<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jnana - AI Co-Scientist Web Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', system-ui, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
            <!-- Header -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <i data-lucide="brain" class="h-6 w-6 text-blue-600"></i>
                    <h1 class="text-lg font-semibold text-gray-900">Jnana</h1>
                </div>
                <p class="text-sm text-gray-500 mt-1">AI Co-Scientist</p>
            </div>

            <!-- Connection Status -->
            <div class="px-4 py-2 border-b border-gray-200 bg-green-50">
                <div class="flex items-center space-x-2">
                    <i data-lucide="wifi" class="h-4 w-4 text-green-600"></i>
                    <span class="text-xs font-medium text-green-700">Connected</span>
                </div>
            </div>

            <!-- Sessions -->
            <div class="flex-1 flex flex-col min-h-0">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-sm font-medium text-gray-900">Research Sessions</h2>
                        <button onclick="createSession()" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700">
                            <i data-lucide="plus" class="h-3 w-3 mr-1"></i>
                            New
                        </button>
                    </div>
                </div>

                <div class="flex-1 overflow-y-auto" id="sessions-list">
                    <div class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer bg-blue-50 border-blue-200" onclick="selectSession('session-1')">
                        <h3 class="text-sm font-medium text-gray-900 truncate">Solar Energy Storage Research</h3>
                        <div class="flex items-center space-x-3 mt-1">
                            <div class="flex items-center text-xs text-gray-500">
                                <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
                                2 hours ago
                            </div>
                            <div class="flex items-center text-xs text-gray-500">
                                <i data-lucide="bar-chart-3" class="h-3 w-3 mr-1"></i>
                                3 hypotheses
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                interactive
                            </span>
                        </div>
                    </div>

                    <div class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer" onclick="selectSession('session-2')">
                        <h3 class="text-sm font-medium text-gray-900 truncate">Climate Change Mitigation Strategies</h3>
                        <div class="flex items-center space-x-3 mt-1">
                            <div class="flex items-center text-xs text-gray-500">
                                <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
                                1 day ago
                            </div>
                            <div class="flex items-center text-xs text-gray-500">
                                <i data-lucide="bar-chart-3" class="h-3 w-3 mr-1"></i>
                                5 hypotheses
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                batch
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Hypotheses -->
                <div class="border-t border-gray-200 flex flex-col min-h-0" style="height: 40%;">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-sm font-medium text-gray-900">Hypotheses</h2>
                    </div>
                    <div class="flex-1 overflow-y-auto" id="hypotheses-list">
                        <div class="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer bg-blue-50 border-blue-200" onclick="selectHypothesis('hyp-1')">
                            <h4 class="text-xs font-medium text-gray-900 truncate">Perovskite-Silicon Tandem Solar Cells</h4>
                            <p class="text-xs text-gray-600 mt-1 line-clamp-2">Enhanced efficiency through novel perovskite materials...</p>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center text-xs text-gray-500">
                                    <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                                    1h ago
                                </div>
                                <span class="text-xs text-gray-400">v1.2</span>
                            </div>
                        </div>

                        <div class="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer" onclick="selectHypothesis('hyp-2')">
                            <h4 class="text-xs font-medium text-gray-900 truncate">Solid-State Battery Integration</h4>
                            <p class="text-xs text-gray-600 mt-1 line-clamp-2">Lithium-metal anodes with ceramic electrolytes...</p>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center text-xs text-gray-500">
                                    <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                                    2h ago
                                </div>
                                <span class="text-xs text-gray-400">v1.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-w-0">
            <!-- Header -->
            <header class="bg-white border-b border-gray-200 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">Solar Energy Storage Research</h1>
                        <p class="text-sm text-gray-500">3 hypotheses • interactive mode</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="p-2 rounded-md hover:bg-gray-100">
                            <i data-lucide="settings" class="h-5 w-5 text-gray-500"></i>
                        </button>
                        <button class="p-2 rounded-md hover:bg-gray-100">
                            <i data-lucide="help-circle" class="h-5 w-5 text-gray-500"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-hidden">
                <div class="flex-1 flex flex-col bg-white" id="main-content">
                    <!-- Hypothesis Header -->
                    <div class="border-b border-gray-200 p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    <h1 class="text-2xl font-bold text-gray-900">Perovskite-Silicon Tandem Solar Cells for Enhanced Energy Storage</h1>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">v1.2</span>
                                </div>
                                
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                        1 hour ago
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="zap" class="h-4 w-4 mr-1"></i>
                                        Literature Exploration
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="target" class="h-4 w-4 mr-1"></i>
                                        improvement
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 overflow-hidden">
                        <div class="h-full flex">
                            <!-- Main Content -->
                            <div class="flex-1 flex flex-col">
                                <!-- Tabs -->
                                <div class="border-b border-gray-200">
                                    <nav class="flex space-x-8 px-6">
                                        <button class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 flex items-center space-x-2">
                                            <i data-lucide="book-open" class="h-4 w-4"></i>
                                            <span>Content</span>
                                        </button>
                                        <button class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-2">
                                            <i data-lucide="target" class="h-4 w-4"></i>
                                            <span>Scientific Hallmarks</span>
                                        </button>
                                        <button class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-2">
                                            <i data-lucide="book-open" class="h-4 w-4"></i>
                                            <span>References</span>
                                        </button>
                                        <button class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-2">
                                            <i data-lucide="message-square" class="h-4 w-4"></i>
                                            <span>Feedback History</span>
                                        </button>
                                    </nav>
                                </div>

                                <!-- Tab Content -->
                                <div class="flex-1 overflow-y-auto p-6">
                                    <div class="prose max-w-none">
                                        <h2>Hypothesis</h2>
                                        <p>Integrating perovskite-silicon tandem solar cells with advanced solid-state battery systems can achieve >30% solar-to-storage efficiency by combining high-efficiency photovoltaic conversion (>35%) with rapid charge storage capabilities.</p>
                                        
                                        <h3>Key Innovation</h3>
                                        <p>The hypothesis proposes a novel integration approach where:</p>
                                        <ul>
                                            <li>Perovskite top cells capture high-energy photons (400-750nm)</li>
                                            <li>Silicon bottom cells utilize transmitted lower-energy photons</li>
                                            <li>Direct coupling with solid-state Li-metal batteries minimizes conversion losses</li>
                                            <li>Smart charge management optimizes storage efficiency</li>
                                        </ul>

                                        <h3>Expected Outcomes</h3>
                                        <p>This approach could potentially:</p>
                                        <ul>
                                            <li>Achieve 35-40% photovoltaic efficiency (vs 26% for silicon alone)</li>
                                            <li>Reduce storage losses to <5% (vs 15-20% for conventional systems)</li>
                                            <li>Enable faster charging cycles (80% capacity in <30 minutes)</li>
                                            <li>Improve overall system durability (>25 year lifespan)</li>
                                        </ul>

                                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                            <h3 class="text-lg font-semibold text-blue-900 mb-2">Experimental Validation</h3>
                                            <p>Proposed validation approach includes:</p>
                                            <ol>
                                                <li>Fabrication of perovskite-silicon tandem prototypes with varying perovskite compositions</li>
                                                <li>Integration testing with solid-state battery modules</li>
                                                <li>Performance characterization under standard test conditions</li>
                                                <li>Long-term stability testing (1000+ charge cycles)</li>
                                                <li>Economic feasibility analysis for commercial deployment</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions Panel -->
                            <div class="w-80 border-l border-gray-200 bg-gray-50 p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                                
                                <!-- Refine Hypothesis -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Provide Feedback to Refine
                                    </label>
                                    <textarea rows="4" class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Describe how you'd like to improve this hypothesis..."></textarea>
                                    <button onclick="refineHypothesis()" class="w-full mt-2 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                                        <i data-lucide="message-square" class="h-4 w-4 mr-2"></i>
                                        Refine Hypothesis
                                    </button>
                                </div>

                                <!-- Generate New -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Generate New Hypothesis
                                    </label>
                                    <select class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-2">
                                        <option>📚 Literature Exploration</option>
                                        <option>🗣️ Scientific Debate</option>
                                        <option>🔍 Assumptions Analysis</option>
                                        <option>🚀 Research Expansion</option>
                                    </select>
                                    <button onclick="generateHypothesis()" class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                                        <i data-lucide="sparkles" class="h-4 w-4 mr-2"></i>
                                        Generate New
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="fixed top-4 right-4 z-50 max-w-sm hidden">
        <div class="p-4 rounded-lg border shadow-lg bg-green-50 border-green-200">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="check-circle" class="h-5 w-5 text-green-600"></i>
                </div>
                <div class="ml-3 flex-1">
                    <h4 class="text-sm font-medium text-gray-900">System Ready</h4>
                    <p class="text-sm text-gray-600 mt-1">Jnana web interface is fully operational</p>
                </div>
                <button onclick="hideNotification()" class="ml-2 flex-shrink-0 p-1 rounded-md hover:bg-white hover:bg-opacity-50">
                    <i data-lucide="x" class="h-4 w-4 text-gray-500"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Show notification on load
        setTimeout(() => {
            document.getElementById('notification').classList.remove('hidden');
            document.getElementById('notification').classList.add('animate-slide-up');
        }, 1000);

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        function createSession() {
            alert('Create Session functionality would integrate with Jnana backend API');
        }

        function selectSession(sessionId) {
            console.log('Selected session:', sessionId);
            // Update UI to show selected session
        }

        function selectHypothesis(hypId) {
            console.log('Selected hypothesis:', hypId);
            // Update UI to show selected hypothesis
        }

        function refineHypothesis() {
            alert('Refine Hypothesis functionality would call Jnana API with feedback');
        }

        function generateHypothesis() {
            alert('Generate Hypothesis functionality would call Jnana API with selected strategy');
        }

        // Simulate real-time updates
        setInterval(() => {
            // This would be replaced with actual WebSocket updates
            console.log('Checking for updates...');
        }, 5000);
    </script>
</body>
</html>
