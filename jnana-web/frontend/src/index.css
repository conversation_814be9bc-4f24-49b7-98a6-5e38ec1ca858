@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .textarea {
    @apply input resize-none;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .card-header {
    @apply flex items-center justify-between pb-4 mb-4 border-b border-gray-200;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-primary-600;
  }
  
  .hypothesis-card {
    @apply card hover:shadow-md transition-shadow cursor-pointer;
  }
  
  .hypothesis-card:hover {
    @apply border-primary-200;
  }
  
  .session-item {
    @apply p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors;
  }
  
  .session-item.active {
    @apply bg-primary-50 border-primary-200;
  }
  
  .code-block {
    @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm font-mono;
  }
  
  .prose-custom {
    @apply prose prose-gray max-w-none;
  }
  
  .prose-custom h1 {
    @apply text-2xl font-bold text-gray-900 mb-4;
  }
  
  .prose-custom h2 {
    @apply text-xl font-semibold text-gray-800 mb-3;
  }
  
  .prose-custom h3 {
    @apply text-lg font-medium text-gray-700 mb-2;
  }
  
  .prose-custom p {
    @apply text-gray-600 mb-3 leading-relaxed;
  }
  
  .prose-custom ul {
    @apply list-disc list-inside text-gray-600 mb-3;
  }
  
  .prose-custom ol {
    @apply list-decimal list-inside text-gray-600 mb-3;
  }
  
  .prose-custom li {
    @apply mb-1;
  }
  
  .prose-custom blockquote {
    @apply border-l-4 border-primary-200 pl-4 italic text-gray-600 my-4;
  }
  
  .prose-custom code {
    @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono;
  }
  
  .prose-custom pre {
    @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm font-mono my-4;
  }
  
  .prose-custom pre code {
    @apply bg-transparent text-inherit p-0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }
}
