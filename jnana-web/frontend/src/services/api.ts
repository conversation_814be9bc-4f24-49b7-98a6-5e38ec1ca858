/**
 * API service for Jnana web interface.
 * 
 * This module provides functions to interact with the Jnana backend API.
 */

import axios, { AxiosResponse } from 'axios';
import {
  Session,
  Hypothesis,
  SystemStatus,
  AvailableModels,
  CreateSessionRequest,
  GenerateHypothesisRequest,
  RefineHypothesisRequest,
  ApiResponse
} from '../types';

// Configure axios defaults
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Helper function to handle API responses
const handleResponse = <T>(response: AxiosResponse<T>): T => {
  return response.data;
};

// Helper function to handle API errors
const handleError = (error: any): never => {
  if (error.response?.data?.error) {
    throw new Error(error.response.data.error);
  } else if (error.message) {
    throw new Error(error.message);
  } else {
    throw new Error('An unexpected error occurred');
  }
};

// Health check
export const healthCheck = async (): Promise<{ status: string; service: string }> => {
  try {
    const response = await api.get('/health');
    return handleResponse(response);
  } catch (error) {
    handleError(error);
  }
};

// Session API
export const sessionApi = {
  // List all sessions
  list: async (): Promise<Session[]> => {
    try {
      const response = await api.get<{ sessions: Session[] }>('/sessions');
      return handleResponse(response).sessions;
    } catch (error) {
      handleError(error);
    }
  },

  // Create a new session
  create: async (data: CreateSessionRequest): Promise<Session> => {
    try {
      const response = await api.post<Session>('/sessions', data);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },

  // Get session details
  get: async (sessionId: string): Promise<Session> => {
    try {
      const response = await api.get<Session>(`/sessions/${sessionId}`);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },

  // Delete a session
  delete: async (sessionId: string): Promise<void> => {
    try {
      await api.delete(`/sessions/${sessionId}`);
    } catch (error) {
      handleError(error);
    }
  },

  // Get session status
  getStatus: async (sessionId: string): Promise<SystemStatus> => {
    try {
      const response = await api.get<SystemStatus>(`/sessions/${sessionId}/status`);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },
};

// Hypothesis API
export const hypothesisApi = {
  // List hypotheses for a session
  list: async (sessionId: string): Promise<Hypothesis[]> => {
    try {
      const response = await api.get<{ hypotheses: Hypothesis[] }>(`/sessions/${sessionId}/hypotheses`);
      return handleResponse(response).hypotheses;
    } catch (error) {
      handleError(error);
    }
  },

  // Generate a new hypothesis
  generate: async (sessionId: string, data: GenerateHypothesisRequest = {}): Promise<Hypothesis> => {
    try {
      const response = await api.post<Hypothesis>(`/sessions/${sessionId}/hypotheses`, data);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },

  // Get a specific hypothesis
  get: async (sessionId: string, hypothesisId: string): Promise<Hypothesis> => {
    try {
      const response = await api.get<Hypothesis>(`/sessions/${sessionId}/hypotheses/${hypothesisId}`);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },

  // Refine a hypothesis
  refine: async (sessionId: string, hypothesisId: string, data: RefineHypothesisRequest): Promise<Hypothesis> => {
    try {
      const response = await api.post<Hypothesis>(`/sessions/${sessionId}/hypotheses/${hypothesisId}/refine`, data);
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },
};

// Models API
export const modelsApi = {
  // Get available models
  list: async (): Promise<AvailableModels> => {
    try {
      const response = await api.get<AvailableModels>('/models');
      return handleResponse(response);
    } catch (error) {
      handleError(error);
    }
  },
};

// Generation strategies
export const GENERATION_STRATEGIES = [
  {
    id: 'literature_exploration',
    name: 'Literature Exploration',
    description: 'Generate hypotheses based on comprehensive literature review and gap analysis',
    icon: '📚',
  },
  {
    id: 'scientific_debate',
    name: 'Scientific Debate',
    description: 'Use multi-agent debate to explore different perspectives and generate robust hypotheses',
    icon: '🗣️',
  },
  {
    id: 'assumptions_identification',
    name: 'Assumptions Analysis',
    description: 'Identify and challenge underlying assumptions to generate novel hypotheses',
    icon: '🔍',
  },
  {
    id: 'research_expansion',
    name: 'Research Expansion',
    description: 'Expand existing research directions to generate new hypotheses',
    icon: '🚀',
  },
];

// Utility functions
export const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString();
};

export const formatRelativeTime = (timestamp: string): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return time.toLocaleDateString();
};

export const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const getStrategyInfo = (strategyId: string) => {
  return GENERATION_STRATEGIES.find(s => s.id === strategyId) || {
    id: strategyId,
    name: strategyId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    description: 'Custom generation strategy',
    icon: '⚡',
  };
};
