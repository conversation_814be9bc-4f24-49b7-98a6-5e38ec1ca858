/**
 * WebSocket service for real-time communication with Jnana backend.
 */

import { io, Socket } from 'socket.io-client';
import { WebSocketEvent } from '../types';

class WebSocketService {
  private socket: Socket | null = null;
  private eventHandlers: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.connect();
  }

  private connect() {
    try {
      this.socket = io('/jnana', {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to Jnana WebSocket');
      this.reconnectAttempts = 0;
      this.emit('connection_status', { connected: true });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from Jnana WebSocket:', reason);
      this.emit('connection_status', { connected: false, reason });
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
        this.emit('connection_error', { error: 'Failed to connect after multiple attempts' });
      }
    });

    // Jnana-specific events
    this.socket.on('session_created', (data) => {
      console.log('Session created:', data);
      this.emit('session_created', data);
    });

    this.socket.on('session_deleted', (data) => {
      console.log('Session deleted:', data);
      this.emit('session_deleted', data);
    });

    this.socket.on('hypothesis_generation_started', (data) => {
      console.log('Hypothesis generation started:', data);
      this.emit('hypothesis_generation_started', data);
    });

    this.socket.on('hypothesis_generated', (data) => {
      console.log('Hypothesis generated:', data);
      this.emit('hypothesis_generated', data);
    });

    this.socket.on('hypothesis_generation_failed', (data) => {
      console.error('Hypothesis generation failed:', data);
      this.emit('hypothesis_generation_failed', data);
    });

    this.socket.on('hypothesis_refinement_started', (data) => {
      console.log('Hypothesis refinement started:', data);
      this.emit('hypothesis_refinement_started', data);
    });

    this.socket.on('hypothesis_refined', (data) => {
      console.log('Hypothesis refined:', data);
      this.emit('hypothesis_refined', data);
    });

    this.socket.on('hypothesis_refinement_failed', (data) => {
      console.error('Hypothesis refinement failed:', data);
      this.emit('hypothesis_refinement_failed', data);
    });
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  public emit(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot emit event:', event);
    }
  }

  public subscribe(event: string, handler: (data: any) => void): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    
    this.eventHandlers.get(event)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(event);
        }
      }
    };
  }

  private emit(event: string, data: any) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in WebSocket event handler for ${event}:`, error);
        }
      });
    }
  }

  public joinSession(sessionId: string) {
    this.emit('join_session', { session_id: sessionId });
  }

  public leaveSession(sessionId: string) {
    this.emit('leave_session', { session_id: sessionId });
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventHandlers.clear();
  }

  public reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    setTimeout(() => this.connect(), 1000);
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
