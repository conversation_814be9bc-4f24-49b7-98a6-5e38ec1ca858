/**
 * React hook for managing hypotheses.
 */

import { useState, useEffect, useCallback } from 'react';
import { Hypothesis, UseHypothesesReturn } from '../types';
import { hypothesisApi } from '../services/api';
import webSocketService from '../services/websocket';

export const useHypotheses = (sessionId: string | null): UseHypothesesReturn => {
  const [hypotheses, setHypotheses] = useState<Hypothesis[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load hypotheses from API
  const refreshHypotheses = useCallback(async () => {
    if (!sessionId) {
      setHypotheses([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const hypothesesList = await hypothesisApi.list(sessionId);
      setHypotheses(hypothesesList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load hypotheses');
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  // Generate a new hypothesis
  const generateHypothesis = useCallback(async (strategy: string): Promise<Hypothesis> => {
    if (!sessionId) {
      throw new Error('No session selected');
    }

    try {
      setError(null);
      const newHypothesis = await hypothesisApi.generate(sessionId, { strategy });
      setHypotheses(prev => [newHypothesis, ...prev]);
      return newHypothesis;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate hypothesis';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [sessionId]);

  // Refine a hypothesis
  const refineHypothesis = useCallback(async (hypothesisId: string, feedback: string): Promise<Hypothesis> => {
    if (!sessionId) {
      throw new Error('No session selected');
    }

    try {
      setError(null);
      const refinedHypothesis = await hypothesisApi.refine(sessionId, hypothesisId, { feedback });
      
      // Update the hypothesis in the list
      setHypotheses(prev => prev.map(hyp => 
        hyp.hypothesis_id === hypothesisId ? refinedHypothesis : hyp
      ));
      
      return refinedHypothesis;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refine hypothesis';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [sessionId]);

  // Set up WebSocket event listeners
  useEffect(() => {
    const unsubscribeGenerated = webSocketService.subscribe('hypothesis_generated', (data) => {
      if (data.session_id === sessionId) {
        setHypotheses(prev => {
          // Check if hypothesis already exists to avoid duplicates
          const exists = prev.some(hyp => hyp.hypothesis_id === data.hypothesis.hypothesis_id);
          if (!exists) {
            return [data.hypothesis, ...prev];
          }
          return prev;
        });
      }
    });

    const unsubscribeRefined = webSocketService.subscribe('hypothesis_refined', (data) => {
      if (data.session_id === sessionId) {
        setHypotheses(prev => prev.map(hyp => 
          hyp.hypothesis_id === data.hypothesis.hypothesis_id ? data.hypothesis : hyp
        ));
      }
    });

    const unsubscribeGenerationStarted = webSocketService.subscribe('hypothesis_generation_started', (data) => {
      if (data.session_id === sessionId) {
        setLoading(true);
        setError(null);
      }
    });

    const unsubscribeGenerationFailed = webSocketService.subscribe('hypothesis_generation_failed', (data) => {
      if (data.session_id === sessionId) {
        setLoading(false);
        setError(data.error || 'Hypothesis generation failed');
      }
    });

    const unsubscribeRefinementStarted = webSocketService.subscribe('hypothesis_refinement_started', (data) => {
      if (data.session_id === sessionId) {
        setLoading(true);
        setError(null);
      }
    });

    const unsubscribeRefinementFailed = webSocketService.subscribe('hypothesis_refinement_failed', (data) => {
      if (data.session_id === sessionId) {
        setLoading(false);
        setError(data.error || 'Hypothesis refinement failed');
      }
    });

    return () => {
      unsubscribeGenerated();
      unsubscribeRefined();
      unsubscribeGenerationStarted();
      unsubscribeGenerationFailed();
      unsubscribeRefinementStarted();
      unsubscribeRefinementFailed();
    };
  }, [sessionId]);

  // Load hypotheses when session changes
  useEffect(() => {
    refreshHypotheses();
  }, [refreshHypotheses]);

  // Join/leave session rooms for WebSocket updates
  useEffect(() => {
    if (sessionId) {
      webSocketService.joinSession(sessionId);
      return () => {
        webSocketService.leaveSession(sessionId);
      };
    }
  }, [sessionId]);

  return {
    hypotheses,
    loading,
    error,
    generateHypothesis,
    refineHypothesis,
    refreshHypotheses,
  };
};
