/**
 * React hook for managing sessions.
 */

import { useState, useEffect, useCallback } from 'react';
import { Session, CreateSessionRequest, UseSessionsReturn } from '../types';
import { sessionApi } from '../services/api';
import webSocketService from '../services/websocket';

export const useSessions = (): UseSessionsReturn => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load sessions from API
  const refreshSessions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const sessionList = await sessionApi.list();
      setSessions(sessionList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions');
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new session
  const createSession = useCallback(async (data: CreateSessionRequest): Promise<Session> => {
    try {
      setError(null);
      const newSession = await sessionApi.create(data);
      setSessions(prev => [newSession, ...prev]);
      return newSession;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Delete a session
  const deleteSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      setError(null);
      await sessionApi.delete(sessionId);
      setSessions(prev => prev.filter(session => session.session_id !== sessionId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete session';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Set up WebSocket event listeners
  useEffect(() => {
    const unsubscribeSessionCreated = webSocketService.subscribe('session_created', (data) => {
      setSessions(prev => {
        // Check if session already exists to avoid duplicates
        const exists = prev.some(session => session.session_id === data.session_id);
        if (!exists) {
          return [data, ...prev];
        }
        return prev;
      });
    });

    const unsubscribeSessionDeleted = webSocketService.subscribe('session_deleted', (data) => {
      setSessions(prev => prev.filter(session => session.session_id !== data.session_id));
    });

    return () => {
      unsubscribeSessionCreated();
      unsubscribeSessionDeleted();
    };
  }, []);

  // Load sessions on mount
  useEffect(() => {
    refreshSessions();
  }, [refreshSessions]);

  return {
    sessions,
    loading,
    error,
    createSession,
    deleteSession,
    refreshSessions,
  };
};
