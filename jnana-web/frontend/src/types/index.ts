/**
 * TypeScript type definitions for Jnana web interface.
 */

export interface Session {
  id: number;
  session_id: string;
  research_goal: string;
  mode: 'interactive' | 'batch' | 'hybrid';
  created_at: string;
  updated_at: string;
  hypothesis_count: number;
}

export interface ScientificHallmarks {
  testability: string;
  specificity: string;
  grounded_knowledge: string;
  predictive_power: string;
  parsimony: string;
}

export interface Reference {
  citation: string;
  annotation: string;
  url?: string;
  doi?: string;
  relevance_score?: number;
}

export interface FeedbackEntry {
  feedback: string;
  timestamp: string;
  version_before: string;
  version_after: string;
  user_id?: string;
  feedback_type: 'user' | 'agent' | 'system';
}

export interface TournamentRecord {
  wins: number;
  losses: number;
  matches_played: number;
  elo_rating: number;
  last_match_timestamp: string;
}

export interface AgentContribution {
  agent_type: string;
  agent_id: string;
  contribution_type: string;
  timestamp: string;
  details: Record<string, any>;
}

export interface Hypothesis {
  id: number;
  hypothesis_id: string;
  session_id: string;
  title: string;
  content: string;
  description: string;
  experimental_validation: string;
  version: number;
  version_string: string;
  hypothesis_type: string;
  hypothesis_number: number;
  generation_strategy: string;
  created_at: string;
  updated_at: string;
  generation_timestamp: string;
  hallmarks: ScientificHallmarks;
  references: Reference[];
  feedback_history: FeedbackEntry[];
  tournament_record: TournamentRecord;
  agent_contributions: AgentContribution[];
  metadata: Record<string, any>;
  notes: string;
  improvements_made: string;
  user_feedback: string;
}

export interface SystemStatus {
  status: 'active' | 'inactive';
  session_id: string;
  mode?: string;
  session?: {
    hypotheses_count: number;
    current_hypothesis?: string;
  };
}

export interface ModelConfig {
  provider: string;
  model: string;
  api_key?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface AvailableModels {
  agents: Record<string, ModelConfig>;
  interactive: Record<string, ModelConfig>;
  local: Record<string, ModelConfig>;
  tasks: Record<string, ModelConfig>;
}

export interface GenerationStrategy {
  id: string;
  name: string;
  description: string;
  icon?: string;
}

export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface CreateSessionRequest {
  research_goal: string;
  mode?: 'interactive' | 'batch' | 'hybrid';
}

export interface GenerateHypothesisRequest {
  strategy?: string;
}

export interface RefineHypothesisRequest {
  feedback: string;
}

// UI State Types
export interface UIState {
  loading: boolean;
  error: string | null;
  selectedSession: string | null;
  selectedHypothesis: string | null;
  sidebarOpen: boolean;
}

export interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Component Props Types
export interface SessionListProps {
  sessions: Session[];
  selectedSession: string | null;
  onSelectSession: (sessionId: string) => void;
  onDeleteSession: (sessionId: string) => void;
  loading?: boolean;
}

export interface HypothesisListProps {
  hypotheses: Hypothesis[];
  selectedHypothesis: string | null;
  onSelectHypothesis: (hypothesisId: string) => void;
  loading?: boolean;
}

export interface HypothesisViewProps {
  hypothesis: Hypothesis | null;
  onRefine: (feedback: string) => void;
  onGenerateNew: (strategy: string) => void;
  loading?: boolean;
}

export interface CreateSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateSessionRequest) => void;
  loading?: boolean;
}

export interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  type?: 'danger' | 'warning' | 'info';
}

// Hook Types
export interface UseSessionsReturn {
  sessions: Session[];
  loading: boolean;
  error: string | null;
  createSession: (data: CreateSessionRequest) => Promise<Session>;
  deleteSession: (sessionId: string) => Promise<void>;
  refreshSessions: () => Promise<void>;
}

export interface UseHypothesesReturn {
  hypotheses: Hypothesis[];
  loading: boolean;
  error: string | null;
  generateHypothesis: (strategy: string) => Promise<Hypothesis>;
  refineHypothesis: (hypothesisId: string, feedback: string) => Promise<Hypothesis>;
  refreshHypotheses: () => Promise<void>;
}

export interface UseWebSocketReturn {
  connected: boolean;
  events: WebSocketEvent[];
  emit: (event: string, data: any) => void;
  subscribe: (event: string, handler: (data: any) => void) => () => void;
}
