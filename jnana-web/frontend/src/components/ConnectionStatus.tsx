/**
 * ConnectionStatus Component - Shows WebSocket connection status.
 */

import React from 'react';
import { Wifi, WifiOff } from 'lucide-react';

interface ConnectionStatusProps {
  connected: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ connected }) => {
  return (
    <div className={`px-4 py-2 border-b border-gray-200 ${connected ? 'bg-green-50' : 'bg-red-50'}`}>
      <div className="flex items-center space-x-2">
        {connected ? (
          <Wifi className="h-4 w-4 text-green-600" />
        ) : (
          <WifiOff className="h-4 w-4 text-red-600" />
        )}
        <span className={`text-xs font-medium ${connected ? 'text-green-700' : 'text-red-700'}`}>
          {connected ? 'Connected' : 'Disconnected'}
        </span>
      </div>
    </div>
  );
};

export default ConnectionStatus;
