/**
 * SessionList Component - Displays list of research sessions.
 */

import React from 'react';
import { Trash2, Calendar, BarChart3 } from 'lucide-react';
import { SessionListProps } from '../types';
import { formatRelativeTime, truncateText } from '../services/api';

const SessionList: React.FC<SessionListProps> = ({
  sessions,
  selectedSession,
  onSelectSession,
  onDeleteSession,
  loading = false,
}) => {
  if (loading && sessions.length === 0) {
    return (
      <div className="p-4 space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (sessions.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm">No research sessions yet</p>
        <p className="text-xs text-gray-400 mt-1">Create your first session to get started</p>
      </div>
    );
  }

  return (
    <div className="overflow-y-auto scrollbar-thin">
      {sessions.map((session) => (
        <div
          key={session.session_id}
          className={`session-item ${selectedSession === session.session_id ? 'active' : ''}`}
          onClick={() => onSelectSession(session.session_id)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {truncateText(session.research_goal, 60)}
              </h3>
              <div className="flex items-center space-x-3 mt-1">
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatRelativeTime(session.created_at)}
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <BarChart3 className="h-3 w-3 mr-1" />
                  {session.hypothesis_count} hypotheses
                </div>
              </div>
              <div className="mt-2">
                <span className={`badge ${
                  session.mode === 'interactive' ? 'badge-primary' :
                  session.mode === 'batch' ? 'badge-success' :
                  'badge-warning'
                }`}>
                  {session.mode}
                </span>
              </div>
            </div>
            
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteSession(session.session_id);
              }}
              className="p-1 rounded-md hover:bg-red-100 text-gray-400 hover:text-red-600 transition-colors"
              title="Delete session"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SessionList;
