/**
 * CreateSessionModal Component - Modal for creating new research sessions.
 */

import React, { useState } from 'react';
import { X, Brain, Zap, Users, Layers } from 'lucide-react';
import { CreateSessionModalProps, CreateSessionRequest } from '../types';

const CreateSessionModal: React.FC<CreateSessionModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formData, setFormData] = useState<CreateSessionRequest>({
    research_goal: '',
    mode: 'interactive',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors: Record<string, string> = {};
    if (!formData.research_goal.trim()) {
      newErrors.research_goal = 'Research goal is required';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setErrors({});
    onSubmit(formData);
  };

  const handleClose = () => {
    setFormData({ research_goal: '', mode: 'interactive' });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  const modes = [
    {
      id: 'interactive',
      name: 'Interactive',
      description: 'Real-time hypothesis generation with user feedback',
      icon: Brain,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
    {
      id: 'batch',
      name: 'Batch',
      description: 'Generate multiple hypotheses automatically',
      icon: Layers,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    {
      id: 'hybrid',
      name: 'Hybrid',
      description: 'Combine batch generation with interactive refinement',
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Brain className="h-6 w-6 text-primary-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Create Research Session</h2>
              <p className="text-sm text-gray-500">Start a new AI-assisted research session</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Research Goal */}
          <div>
            <label htmlFor="research_goal" className="block text-sm font-medium text-gray-700 mb-2">
              Research Goal or Question
            </label>
            <textarea
              id="research_goal"
              rows={4}
              className={`textarea ${errors.research_goal ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}`}
              placeholder="Describe your research question or goal. For example: 'How can we develop more effective treatments for neurodegenerative diseases?' or 'What are novel approaches to renewable energy storage?'"
              value={formData.research_goal}
              onChange={(e) => setFormData({ ...formData, research_goal: e.target.value })}
              disabled={loading}
            />
            {errors.research_goal && (
              <p className="mt-1 text-sm text-red-600">{errors.research_goal}</p>
            )}
          </div>

          {/* Mode Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Session Mode
            </label>
            <div className="grid grid-cols-1 gap-3">
              {modes.map((mode) => {
                const Icon = mode.icon;
                const isSelected = formData.mode === mode.id;
                
                return (
                  <label
                    key={mode.id}
                    className={`relative flex items-start p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      isSelected
                        ? `${mode.borderColor} ${mode.bgColor}`
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <input
                      type="radio"
                      name="mode"
                      value={mode.id}
                      checked={isSelected}
                      onChange={(e) => setFormData({ ...formData, mode: e.target.value as any })}
                      className="sr-only"
                      disabled={loading}
                    />
                    <div className={`flex-shrink-0 p-2 rounded-lg ${isSelected ? mode.bgColor : 'bg-gray-50'}`}>
                      <Icon className={`h-5 w-5 ${isSelected ? mode.color : 'text-gray-400'}`} />
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="flex items-center">
                        <span className={`text-sm font-medium ${isSelected ? 'text-gray-900' : 'text-gray-700'}`}>
                          {mode.name}
                        </span>
                        {mode.id === 'batch' && (
                          <span className="ml-2 badge badge-warning text-xs">Beta</span>
                        )}
                      </div>
                      <p className={`text-sm mt-1 ${isSelected ? 'text-gray-700' : 'text-gray-500'}`}>
                        {mode.description}
                      </p>
                    </div>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="btn-outline"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="loading-spinner mr-2"></div>
                  Creating...
                </div>
              ) : (
                'Create Session'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateSessionModal;
