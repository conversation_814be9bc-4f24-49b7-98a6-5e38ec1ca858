/**
 * NotificationCenter Component - Displays system notifications.
 */

import React, { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { NotificationState } from '../types';
import webSocketService from '../services/websocket';

const NotificationCenter: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationState[]>([]);

  useEffect(() => {
    // Subscribe to WebSocket events for notifications
    const unsubscribes = [
      webSocketService.subscribe('hypothesis_generated', (data) => {
        addNotification({
          type: 'success',
          title: 'Hypothesis Generated',
          message: `New hypothesis "${data.hypothesis.title}" has been generated successfully.`,
        });
      }),

      webSocketService.subscribe('hypothesis_generation_failed', (data) => {
        addNotification({
          type: 'error',
          title: 'Generation Failed',
          message: data.error || 'Failed to generate hypothesis.',
        });
      }),

      webSocketService.subscribe('hypothesis_refined', (data) => {
        addNotification({
          type: 'success',
          title: 'Hypothesis Refined',
          message: `Hypothesis "${data.hypothesis.title}" has been refined to version ${data.hypothesis.version_string}.`,
        });
      }),

      webSocketService.subscribe('hypothesis_refinement_failed', (data) => {
        addNotification({
          type: 'error',
          title: 'Refinement Failed',
          message: data.error || 'Failed to refine hypothesis.',
        });
      }),

      webSocketService.subscribe('session_created', (data) => {
        addNotification({
          type: 'info',
          title: 'Session Created',
          message: 'New research session has been created successfully.',
        });
      }),

      webSocketService.subscribe('connection_status', (data) => {
        if (!data.connected) {
          addNotification({
            type: 'warning',
            title: 'Connection Lost',
            message: 'WebSocket connection has been lost. Attempting to reconnect...',
          });
        } else {
          addNotification({
            type: 'success',
            title: 'Connected',
            message: 'WebSocket connection has been established.',
          });
        }
      }),
    ];

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, []);

  const addNotification = (notification: Omit<NotificationState, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: NotificationState = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Keep only 5 notifications

    // Auto-remove after 5 seconds for success/info notifications
    if (notification.type === 'success' || notification.type === 'info') {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getIcon = (type: NotificationState['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />;
      default:
        return <Info className="h-5 w-5 text-gray-600" />;
    }
  };

  const getBackgroundColor = (type: NotificationState['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`p-4 rounded-lg border shadow-lg animate-slide-up ${getBackgroundColor(notification.type)}`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {getIcon(notification.type)}
            </div>
            <div className="ml-3 flex-1">
              <h4 className="text-sm font-medium text-gray-900">
                {notification.title}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {notification.message}
              </p>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="ml-2 flex-shrink-0 p-1 rounded-md hover:bg-white hover:bg-opacity-50 transition-colors"
            >
              <X className="h-4 w-4 text-gray-500" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationCenter;
