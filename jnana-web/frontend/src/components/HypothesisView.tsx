/**
 * HypothesisView Component - Main view for displaying and interacting with hypotheses.
 */

import React, { useState } from 'react';
import { 
  Sparkles, 
  MessageSquare, 
  BookOpen, 
  Target, 
  Zap, 
  Clock,
  User,
  Bot,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { HypothesisViewProps } from '../types';
import { GENERATION_STRATEGIES, formatRelativeTime, getStrategyInfo } from '../services/api';

const HypothesisView: React.FC<HypothesisViewProps> = ({
  hypothesis,
  onRefine,
  onGenerateNew,
  loading = false,
}) => {
  const [feedback, setFeedback] = useState('');
  const [selectedStrategy, setSelectedStrategy] = useState('literature_exploration');
  const [showDetails, setShowDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'content' | 'hallmarks' | 'references' | 'feedback'>('content');

  const handleRefine = () => {
    if (feedback.trim()) {
      onRefine(feedback);
      setFeedback('');
    }
  };

  const handleGenerateNew = () => {
    onGenerateNew(selectedStrategy);
  };

  if (!hypothesis) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Ready to Generate Hypotheses</h2>
          <p className="text-gray-600 mb-6">
            Select a research session and generate your first hypothesis to get started with AI-assisted research.
          </p>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <select
                value={selectedStrategy}
                onChange={(e) => setSelectedStrategy(e.target.value)}
                className="input flex-1"
              >
                {GENERATION_STRATEGIES.map((strategy) => (
                  <option key={strategy.id} value={strategy.id}>
                    {strategy.icon} {strategy.name}
                  </option>
                ))}
              </select>
              <button
                onClick={handleGenerateNew}
                className="btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="loading-spinner mr-2"></div>
                    Generating...
                  </div>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const strategy = getStrategyInfo(hypothesis.generation_strategy);

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{hypothesis.title}</h1>
              <span className="badge badge-primary">v{hypothesis.version_string}</span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {formatRelativeTime(hypothesis.created_at)}
              </div>
              <div className="flex items-center">
                <Zap className="h-4 w-4 mr-1" />
                {strategy.name}
              </div>
              <div className="flex items-center">
                <Target className="h-4 w-4 mr-1" />
                {hypothesis.hypothesis_type}
              </div>
            </div>
          </div>
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="btn-outline"
          >
            {showDetails ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                Show Details
              </>
            )}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex">
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'content', label: 'Content', icon: BookOpen },
                  { id: 'hallmarks', label: 'Scientific Hallmarks', icon: Target },
                  { id: 'references', label: 'References', icon: BookOpen },
                  { id: 'feedback', label: 'Feedback History', icon: MessageSquare },
                ].map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                        activeTab === tab.id
                          ? 'border-primary-500 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {activeTab === 'content' && (
                <div className="prose-custom">
                  <ReactMarkdown>{hypothesis.content}</ReactMarkdown>
                  {hypothesis.experimental_validation && (
                    <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                      <h3 className="text-lg font-semibold text-blue-900 mb-2">Experimental Validation</h3>
                      <ReactMarkdown>{hypothesis.experimental_validation}</ReactMarkdown>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'hallmarks' && (
                <div className="space-y-4">
                  {Object.entries(hypothesis.hallmarks || {}).map(([key, value]) => (
                    <div key={key} className="card">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 capitalize">
                        {key.replace(/_/g, ' ')}
                      </h3>
                      <div className="prose-custom">
                        <ReactMarkdown>{value as string}</ReactMarkdown>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'references' && (
                <div className="space-y-4">
                  {hypothesis.references?.map((ref, index) => (
                    <div key={index} className="card">
                      <h4 className="font-medium text-gray-900 mb-2">{ref.citation}</h4>
                      {ref.annotation && (
                        <p className="text-gray-600 mb-2">{ref.annotation}</p>
                      )}
                      {ref.url && (
                        <a
                          href={ref.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-600 hover:text-primary-700 text-sm"
                        >
                          View Source →
                        </a>
                      )}
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-8">No references available</p>
                  )}
                </div>
              )}

              {activeTab === 'feedback' && (
                <div className="space-y-4">
                  {hypothesis.feedback_history?.map((feedback, index) => (
                    <div key={index} className="card">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {feedback.feedback_type === 'user' ? (
                            <User className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Bot className="h-5 w-5 text-green-600" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-gray-900 capitalize">
                              {feedback.feedback_type}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatRelativeTime(feedback.timestamp)}
                            </span>
                            <span className="text-xs text-gray-400">
                              {feedback.version_before} → {feedback.version_after}
                            </span>
                          </div>
                          <p className="text-gray-700">{feedback.feedback}</p>
                        </div>
                      </div>
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-8">No feedback history</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Actions Panel */}
          <div className="w-80 border-l border-gray-200 bg-gray-50 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
            
            {/* Refine Hypothesis */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Provide Feedback to Refine
              </label>
              <textarea
                rows={4}
                className="textarea"
                placeholder="Describe how you'd like to improve this hypothesis..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                disabled={loading}
              />
              <button
                onClick={handleRefine}
                className="btn-primary w-full mt-2"
                disabled={!feedback.trim() || loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="loading-spinner mr-2"></div>
                    Refining...
                  </div>
                ) : (
                  <>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Refine Hypothesis
                  </>
                )}
              </button>
            </div>

            {/* Generate New */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Generate New Hypothesis
              </label>
              <select
                value={selectedStrategy}
                onChange={(e) => setSelectedStrategy(e.target.value)}
                className="input mb-2"
                disabled={loading}
              >
                {GENERATION_STRATEGIES.map((strategy) => (
                  <option key={strategy.id} value={strategy.id}>
                    {strategy.icon} {strategy.name}
                  </option>
                ))}
              </select>
              <button
                onClick={handleGenerateNew}
                className="btn-secondary w-full"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="loading-spinner mr-2"></div>
                    Generating...
                  </div>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate New
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HypothesisView;
