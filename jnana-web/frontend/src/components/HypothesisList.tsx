/**
 * HypothesisList Component - Displays list of hypotheses for a session.
 */

import React from 'react';
import { FileText, Clock, Zap } from 'lucide-react';
import { HypothesisListProps } from '../types';
import { formatRelativeTime, truncateText, getStrategyInfo } from '../services/api';

const HypothesisList: React.FC<HypothesisListProps> = ({
  hypotheses,
  selectedHypothesis,
  onSelectHypothesis,
  loading = false,
}) => {
  if (loading && hypotheses.length === 0) {
    return (
      <div className="p-3 space-y-2">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="animate-pulse p-2 border rounded">
            <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
            <div className="h-2 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (hypotheses.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <FileText className="h-6 w-6 mx-auto mb-2 text-gray-400" />
        <p className="text-xs">No hypotheses yet</p>
        <p className="text-xs text-gray-400 mt-1">Generate your first hypothesis</p>
      </div>
    );
  }

  return (
    <div className="overflow-y-auto scrollbar-thin">
      {hypotheses.map((hypothesis) => {
        const strategy = getStrategyInfo(hypothesis.generation_strategy);
        
        return (
          <div
            key={hypothesis.hypothesis_id}
            className={`p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
              selectedHypothesis === hypothesis.hypothesis_id ? 'bg-primary-50 border-primary-200' : ''
            }`}
            onClick={() => onSelectHypothesis(hypothesis.hypothesis_id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-xs font-medium text-gray-900 truncate">
                  {truncateText(hypothesis.title, 40)}
                </h4>
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {truncateText(hypothesis.description || hypothesis.content, 60)}
                </p>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatRelativeTime(hypothesis.created_at)}
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-400">v{hypothesis.version_string}</span>
                    {hypothesis.generation_strategy && (
                      <div className="flex items-center text-xs text-gray-500" title={strategy.description}>
                        <Zap className="h-3 w-3 mr-1" />
                        <span className="truncate max-w-16">{strategy.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default HypothesisList;
