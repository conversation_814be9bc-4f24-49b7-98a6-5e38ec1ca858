/**
 * Main Jnana Web Application Component.
 */

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Brain, Menu, X, Plus, Settings, HelpCircle } from 'lucide-react';

// Components
import SessionList from './components/SessionList';
import HypothesisList from './components/HypothesisList';
import HypothesisView from './components/HypothesisView';
import CreateSessionModal from './components/CreateSessionModal';
import NotificationCenter from './components/NotificationCenter';
import ConnectionStatus from './components/ConnectionStatus';

// Hooks
import { useSessions } from './hooks/useSessions';
import { useHypotheses } from './hooks/useHypotheses';

// Services
import webSocketService from './services/websocket';

// Types
import { CreateSessionRequest } from './types';

const App: React.FC = () => {
  // UI State
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [selectedHypothesis, setSelectedHypothesis] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [createSessionModalOpen, setCreateSessionModalOpen] = useState(false);
  const [connected, setConnected] = useState(false);

  // Data hooks
  const { sessions, loading: sessionsLoading, error: sessionsError, createSession, deleteSession } = useSessions();
  const { hypotheses, loading: hypothesesLoading, error: hypothesesError, generateHypothesis, refineHypothesis } = useHypotheses(selectedSession);

  // WebSocket connection status
  useEffect(() => {
    const unsubscribe = webSocketService.subscribe('connection_status', (data) => {
      setConnected(data.connected);
    });

    // Initial connection status
    setConnected(webSocketService.isConnected());

    return unsubscribe;
  }, []);

  // Handle session selection
  const handleSelectSession = (sessionId: string) => {
    setSelectedSession(sessionId);
    setSelectedHypothesis(null);
  };

  // Handle hypothesis selection
  const handleSelectHypothesis = (hypothesisId: string) => {
    setSelectedHypothesis(hypothesisId);
  };

  // Handle session creation
  const handleCreateSession = async (data: CreateSessionRequest) => {
    try {
      const newSession = await createSession(data);
      setSelectedSession(newSession.session_id);
      setCreateSessionModalOpen(false);
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  };

  // Handle session deletion
  const handleDeleteSession = async (sessionId: string) => {
    try {
      await deleteSession(sessionId);
      if (selectedSession === sessionId) {
        setSelectedSession(null);
        setSelectedHypothesis(null);
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  };

  // Handle hypothesis generation
  const handleGenerateHypothesis = async (strategy: string) => {
    if (!selectedSession) return;
    
    try {
      const newHypothesis = await generateHypothesis(strategy);
      setSelectedHypothesis(newHypothesis.hypothesis_id);
    } catch (error) {
      console.error('Failed to generate hypothesis:', error);
    }
  };

  // Handle hypothesis refinement
  const handleRefineHypothesis = async (feedback: string) => {
    if (!selectedSession || !selectedHypothesis) return;
    
    try {
      await refineHypothesis(selectedHypothesis, feedback);
    } catch (error) {
      console.error('Failed to refine hypothesis:', error);
    }
  };

  // Get selected hypothesis object
  const selectedHypothesisObj = hypotheses.find(h => h.hypothesis_id === selectedHypothesis) || null;

  return (
    <Router>
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-white border-r border-gray-200 flex flex-col`}>
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Brain className="h-6 w-6 text-primary-600" />
                <h1 className="text-lg font-semibold text-gray-900">Jnana</h1>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-1 rounded-md hover:bg-gray-100"
              >
                <X className="h-4 w-4 text-gray-500" />
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-1">AI Co-Scientist</p>
          </div>

          {/* Connection Status */}
          <ConnectionStatus connected={connected} />

          {/* Sessions Section */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-sm font-medium text-gray-900">Research Sessions</h2>
                <button
                  onClick={() => setCreateSessionModalOpen(true)}
                  className="btn-primary text-xs px-2 py-1"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  New
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-hidden">
              <SessionList
                sessions={sessions}
                selectedSession={selectedSession}
                onSelectSession={handleSelectSession}
                onDeleteSession={handleDeleteSession}
                loading={sessionsLoading}
              />
            </div>
          </div>

          {/* Hypotheses Section */}
          {selectedSession && (
            <div className="border-t border-gray-200 flex flex-col min-h-0" style={{ height: '40%' }}>
              <div className="p-4 border-b border-gray-200">
                <h2 className="text-sm font-medium text-gray-900">Hypotheses</h2>
              </div>
              <div className="flex-1 overflow-hidden">
                <HypothesisList
                  hypotheses={hypotheses}
                  selectedHypothesis={selectedHypothesis}
                  onSelectHypothesis={handleSelectHypothesis}
                  loading={hypothesesLoading}
                />
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Header */}
          <header className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {!sidebarOpen && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="p-2 rounded-md hover:bg-gray-100"
                  >
                    <Menu className="h-5 w-5 text-gray-500" />
                  </button>
                )}
                
                <div>
                  {selectedSession ? (
                    <div>
                      <h1 className="text-lg font-semibold text-gray-900">
                        {sessions.find(s => s.session_id === selectedSession)?.research_goal || 'Research Session'}
                      </h1>
                      <p className="text-sm text-gray-500">
                        {hypotheses.length} hypotheses • {sessions.find(s => s.session_id === selectedSession)?.mode} mode
                      </p>
                    </div>
                  ) : (
                    <div>
                      <h1 className="text-lg font-semibold text-gray-900">Welcome to Jnana</h1>
                      <p className="text-sm text-gray-500">Select a session or create a new one to get started</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button className="p-2 rounded-md hover:bg-gray-100">
                  <Settings className="h-5 w-5 text-gray-500" />
                </button>
                <button className="p-2 rounded-md hover:bg-gray-100">
                  <HelpCircle className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            </div>
          </header>

          {/* Main Content Area */}
          <main className="flex-1 overflow-hidden">
            <Routes>
              <Route
                path="/"
                element={
                  <HypothesisView
                    hypothesis={selectedHypothesisObj}
                    onRefine={handleRefineHypothesis}
                    onGenerateNew={handleGenerateHypothesis}
                    loading={hypothesesLoading}
                  />
                }
              />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </main>
        </div>

        {/* Modals and Overlays */}
        <CreateSessionModal
          isOpen={createSessionModalOpen}
          onClose={() => setCreateSessionModalOpen(false)}
          onSubmit={handleCreateSession}
        />

        <NotificationCenter />
      </div>
    </Router>
  );
};

export default App;
