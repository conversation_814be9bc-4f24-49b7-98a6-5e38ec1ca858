"""
Flask routes for Jnana web API.

This module defines the REST API endpoints for the Jnana web interface.
"""

import asyncio
from flask import Blueprint, request, jsonify, current_app
from flask_socketio import emit
from . import socketio

api_bp = Blueprint('api', __name__)


def run_async(coro):
    """Helper to run async functions in Flask routes."""
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, we need to use a different approach
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        # No event loop exists, create a new one
        return asyncio.run(coro)


@api_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'jnana-web'})


@api_bp.route('/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        sessions = current_app.jnana_service.list_sessions()
        return jsonify({'sessions': sessions})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions', methods=['POST'])
def create_session():
    """Create a new session."""
    try:
        data = request.get_json()
        research_goal = data.get('research_goal')
        mode = data.get('mode', 'interactive')

        if not research_goal:
            return jsonify({'error': 'research_goal is required'}), 400

        session = current_app.jnana_service.create_session(research_goal, mode)

        # Emit session created event
        socketio.emit('session_created', session, namespace='/jnana')

        return jsonify(session), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get session details."""
    try:
        session = run_async(current_app.jnana_service.get_session(session_id))
        if not session:
            return jsonify({'error': 'Session not found'}), 404
        return jsonify(session)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    """Delete a session."""
    try:
        success = run_async(current_app.jnana_service.delete_session(session_id))
        if not success:
            return jsonify({'error': 'Session not found'}), 404
        
        # Emit session deleted event
        socketio.emit('session_deleted', {'session_id': session_id}, namespace='/jnana')
        
        return jsonify({'message': 'Session deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/hypotheses', methods=['GET'])
def get_session_hypotheses(session_id):
    """Get all hypotheses for a session."""
    try:
        hypotheses = run_async(current_app.jnana_service.get_session_hypotheses(session_id))
        return jsonify({'hypotheses': hypotheses})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/hypotheses', methods=['POST'])
def generate_hypothesis(session_id):
    """Generate a new hypothesis for a session."""
    try:
        data = request.get_json() or {}
        strategy = data.get('strategy', 'literature_exploration')
        
        # Emit generation started event
        socketio.emit('hypothesis_generation_started', {
            'session_id': session_id,
            'strategy': strategy
        }, namespace='/jnana')
        
        hypothesis = run_async(current_app.jnana_service.generate_hypothesis(session_id, strategy))
        
        # Emit generation completed event
        socketio.emit('hypothesis_generated', {
            'session_id': session_id,
            'hypothesis': hypothesis
        }, namespace='/jnana')
        
        return jsonify(hypothesis), 201
    except Exception as e:
        # Emit generation failed event
        socketio.emit('hypothesis_generation_failed', {
            'session_id': session_id,
            'error': str(e)
        }, namespace='/jnana')

        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/hypotheses/<hypothesis_id>/notes', methods=['PUT'])
def update_hypothesis_notes(session_id, hypothesis_id):
    """Update hypothesis notes."""
    try:
        data = request.get_json()
        notes = data.get('notes', '')

        # Update notes in database
        from .models import Hypothesis, db
        hypothesis = Hypothesis.query.filter_by(
            session_id=session_id,
            hypothesis_id=hypothesis_id
        ).first()

        if not hypothesis:
            return jsonify({'error': 'Hypothesis not found'}), 404

        hypothesis.notes = notes
        db.session.commit()

        return jsonify({'message': 'Notes updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/hypotheses/<hypothesis_id>', methods=['GET'])
def get_hypothesis(session_id, hypothesis_id):
    """Get a specific hypothesis."""
    try:
        hypothesis = run_async(current_app.jnana_service.get_hypothesis(session_id, hypothesis_id))
        if not hypothesis:
            return jsonify({'error': 'Hypothesis not found'}), 404
        return jsonify(hypothesis)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/hypotheses/<hypothesis_id>/refine', methods=['POST'])
def refine_hypothesis(session_id, hypothesis_id):
    """Refine a hypothesis based on feedback."""
    try:
        data = request.get_json()
        feedback = data.get('feedback')
        
        if not feedback:
            return jsonify({'error': 'feedback is required'}), 400
        
        # Emit refinement started event
        socketio.emit('hypothesis_refinement_started', {
            'session_id': session_id,
            'hypothesis_id': hypothesis_id,
            'feedback': feedback
        }, namespace='/jnana')
        
        refined_hypothesis = run_async(current_app.jnana_service.refine_hypothesis(
            session_id, hypothesis_id, feedback
        ))
        
        # Emit refinement completed event
        socketio.emit('hypothesis_refined', {
            'session_id': session_id,
            'hypothesis': refined_hypothesis
        }, namespace='/jnana')
        
        return jsonify(refined_hypothesis)
    except Exception as e:
        # Emit refinement failed event
        socketio.emit('hypothesis_refinement_failed', {
            'session_id': session_id,
            'hypothesis_id': hypothesis_id,
            'error': str(e)
        }, namespace='/jnana')
        
        return jsonify({'error': str(e)}), 500


@api_bp.route('/sessions/<session_id>/status', methods=['GET'])
def get_session_status(session_id):
    """Get system status for a session."""
    try:
        status = run_async(current_app.jnana_service.get_system_status(session_id))
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@api_bp.route('/models', methods=['GET'])
def get_available_models():
    """Get available models."""
    try:
        models = run_async(current_app.jnana_service.get_available_models())
        return jsonify(models)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


# WebSocket event handlers
@socketio.on('connect', namespace='/jnana')
def handle_connect():
    """Handle client connection."""
    emit('connected', {'message': 'Connected to Jnana WebSocket'})


@socketio.on('disconnect', namespace='/jnana')
def handle_disconnect():
    """Handle client disconnection."""
    print('Client disconnected from Jnana WebSocket')


@socketio.on('join_session', namespace='/jnana')
def handle_join_session(data):
    """Handle client joining a session room."""
    session_id = data.get('session_id')
    if session_id:
        # Join the session room for targeted updates
        from flask_socketio import join_room
        join_room(session_id)
        emit('joined_session', {'session_id': session_id})


@socketio.on('leave_session', namespace='/jnana')
def handle_leave_session(data):
    """Handle client leaving a session room."""
    session_id = data.get('session_id')
    if session_id:
        from flask_socketio import leave_room
        leave_room(session_id)
        emit('left_session', {'session_id': session_id})
