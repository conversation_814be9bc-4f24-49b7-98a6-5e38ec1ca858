"""
Jnana Web Service Layer.

This module provides the service layer that integrates the web interface
with the core Jnana system.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from jnana import JnanaSystem
from jnana.data.unified_hypothesis import UnifiedHypothesis
from .models import Session, Hypothesis, db


class JnanaWebService:
    """
    Service layer for integrating Jnana core system with web interface.
    
    This service manages the lifecycle of Jnana sessions and provides
    a bridge between the web API and the core Jnana functionality.
    """
    
    def __init__(self, config_path: str):
        """
        Initialize the Jnana web service.
        
        Args:
            config_path: Path to Jnana configuration file
        """
        self.config_path = config_path
        self.jnana_systems: Dict[str, JnanaSystem] = {}
        self.logger = logging.getLogger(__name__)
        
    def create_session(self, research_goal: str, mode: str = 'interactive') -> Dict[str, Any]:
        """
        Create a new research session (simplified synchronous version).

        Args:
            research_goal: The research question or goal
            mode: Session mode ('interactive', 'batch', 'hybrid')

        Returns:
            Dictionary containing session information
        """
        try:
            # Generate session ID
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

            # Create session data
            session_data = {
                'session_id': session_id,
                'research_goal': research_goal,
                'mode': mode,
                'status': 'active',
                'created_at': datetime.now().isoformat()
            }

            # Store in memory for now (simplified)
            if not hasattr(self, 'sessions'):
                self.sessions = {}
            self.sessions[session_id] = session_data

            self.logger.info(f"Created session {session_id} with goal: {research_goal[:100]}...")

            return session_data

        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session information.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session information dictionary or None if not found
        """
        session = Session.query.filter_by(session_id=session_id).first()
        if not session:
            return None
            
        return session.to_dict()
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """
        List all sessions.

        Returns:
            List of session dictionaries
        """
        # Return sessions from memory storage (simplified)
        if hasattr(self, 'sessions'):
            return list(self.sessions.values())
        return []
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            # Stop Jnana system if running
            if session_id in self.jnana_systems:
                await self.jnana_systems[session_id].stop()
                del self.jnana_systems[session_id]
            
            # Delete from database
            session = Session.query.filter_by(session_id=session_id).first()
            if session:
                db.session.delete(session)
                db.session.commit()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to delete session {session_id}: {e}")
            return False

    async def generate_hypothesis(self, session_id: str, strategy: str = 'literature_exploration') -> Dict[str, Any]:
        """
        Generate a new hypothesis for a session.

        Args:
            session_id: Session identifier
            strategy: Generation strategy

        Returns:
            Generated hypothesis dictionary
        """
        try:
            # Get Jnana system instance
            jnana = self.jnana_systems.get(session_id)
            if not jnana:
                raise ValueError(f"Session {session_id} not found or not active")

            # Generate hypothesis using Jnana
            unified_hyp = await jnana.generate_single_hypothesis(strategy)

            # Save to database
            hypothesis = Hypothesis(session_id=session_id)
            hypothesis.from_unified_hypothesis(unified_hyp)

            db.session.add(hypothesis)
            db.session.commit()

            self.logger.info(f"Generated hypothesis {unified_hyp.hypothesis_id} for session {session_id}")

            return hypothesis.to_dict()

        except Exception as e:
            self.logger.error(f"Failed to generate hypothesis for session {session_id}: {e}")
            raise

    async def refine_hypothesis(self, session_id: str, hypothesis_id: str, feedback: str) -> Dict[str, Any]:
        """
        Refine an existing hypothesis based on feedback.

        Args:
            session_id: Session identifier
            hypothesis_id: Hypothesis identifier
            feedback: User feedback for refinement

        Returns:
            Refined hypothesis dictionary
        """
        try:
            # Get Jnana system instance
            jnana = self.jnana_systems.get(session_id)
            if not jnana:
                raise ValueError(f"Session {session_id} not found or not active")

            # Get original hypothesis from database
            hypothesis = Hypothesis.query.filter_by(
                session_id=session_id,
                hypothesis_id=hypothesis_id
            ).first()

            if not hypothesis:
                raise ValueError(f"Hypothesis {hypothesis_id} not found")

            # Convert to UnifiedHypothesis for refinement
            unified_hyp = UnifiedHypothesis(
                hypothesis_id=hypothesis.hypothesis_id,
                title=hypothesis.title,
                content=hypothesis.content,
                description=hypothesis.description
            )

            # Refine using Jnana
            refined_hyp = await jnana.refine_hypothesis(unified_hyp, feedback)

            # Update database record
            hypothesis.from_unified_hypothesis(refined_hyp)
            db.session.commit()

            self.logger.info(f"Refined hypothesis {hypothesis_id} for session {session_id}")

            return hypothesis.to_dict()

        except Exception as e:
            self.logger.error(f"Failed to refine hypothesis {hypothesis_id}: {e}")
            raise

    async def get_session_hypotheses(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get all hypotheses for a session.

        Args:
            session_id: Session identifier

        Returns:
            List of hypothesis dictionaries
        """
        hypotheses = Hypothesis.query.filter_by(session_id=session_id).order_by(
            Hypothesis.created_at.desc()
        ).all()

        return [hyp.to_dict() for hyp in hypotheses]

    async def get_hypothesis(self, session_id: str, hypothesis_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific hypothesis.

        Args:
            session_id: Session identifier
            hypothesis_id: Hypothesis identifier

        Returns:
            Hypothesis dictionary or None if not found
        """
        hypothesis = Hypothesis.query.filter_by(
            session_id=session_id,
            hypothesis_id=hypothesis_id
        ).first()

        if not hypothesis:
            return None

        return hypothesis.to_dict()

    async def get_system_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get system status for a session.

        Args:
            session_id: Session identifier

        Returns:
            System status dictionary
        """
        jnana = self.jnana_systems.get(session_id)
        if not jnana:
            return {'status': 'inactive', 'session_id': session_id}

        status = jnana.get_system_status()
        return status

    async def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models from Jnana configuration.

        Returns:
            Dictionary of available models
        """
        try:
            from jnana.core.model_manager import UnifiedModelManager
            manager = UnifiedModelManager(self.config_path)
            models = manager.list_available_models()
            return models
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return {}

    async def cleanup(self):
        """Clean up all active Jnana systems."""
        for session_id, jnana in self.jnana_systems.items():
            try:
                await jnana.stop()
            except Exception as e:
                self.logger.error(f"Error stopping session {session_id}: {e}")

        self.jnana_systems.clear()
