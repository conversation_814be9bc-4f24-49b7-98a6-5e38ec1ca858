"""
Database models for Jnana web interface.

This module defines SQLAlchemy models for storing sessions, hypotheses,
and other data in the web interface.
"""

from datetime import datetime
from . import db
import json


class Session(db.Model):
    """Research session model."""
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(36), unique=True, nullable=False)
    research_goal = db.Column(db.Text, nullable=False)
    mode = db.Column(db.String(20), default='interactive')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship to hypotheses
    hypotheses = db.relationship('Hypothesis', backref='session', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        """Convert session to dictionary."""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'research_goal': self.research_goal,
            'mode': self.mode,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'hypothesis_count': len(self.hypotheses)
        }


class Hypothesis(db.Model):
    """Hypothesis model."""
    
    id = db.Column(db.Integer, primary_key=True)
    hypothesis_id = db.Column(db.String(36), unique=True, nullable=False)
    session_id = db.Column(db.String(36), db.ForeignKey('session.session_id'), nullable=False)
    
    # Core content
    title = db.Column(db.String(500), nullable=False)
    content = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    experimental_validation = db.Column(db.Text)
    
    # Metadata
    version = db.Column(db.Integer, default=1)
    version_string = db.Column(db.String(10), default='1.0')
    hypothesis_type = db.Column(db.String(50), default='original')
    hypothesis_number = db.Column(db.Integer, default=1)
    generation_strategy = db.Column(db.String(100))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    generation_timestamp = db.Column(db.String(50))
    
    # JSON fields for complex data
    hallmarks_json = db.Column(db.Text)  # Scientific hallmarks
    references_json = db.Column(db.Text)  # References
    feedback_history_json = db.Column(db.Text)  # Feedback history
    tournament_record_json = db.Column(db.Text)  # Tournament data
    agent_contributions_json = db.Column(db.Text)  # Agent contributions
    metadata_json = db.Column(db.Text)  # Additional metadata
    
    # User interaction
    notes = db.Column(db.Text)
    improvements_made = db.Column(db.Text)
    user_feedback = db.Column(db.Text)
    
    @property
    def hallmarks(self):
        """Get hallmarks as dictionary."""
        if self.hallmarks_json:
            return json.loads(self.hallmarks_json)
        return {}
    
    @hallmarks.setter
    def hallmarks(self, value):
        """Set hallmarks from dictionary."""
        self.hallmarks_json = json.dumps(value) if value else None
    
    @property
    def references(self):
        """Get references as list."""
        if self.references_json:
            return json.loads(self.references_json)
        return []
    
    @references.setter
    def references(self, value):
        """Set references from list."""
        self.references_json = json.dumps(value) if value else None
    
    @property
    def feedback_history(self):
        """Get feedback history as list."""
        if self.feedback_history_json:
            return json.loads(self.feedback_history_json)
        return []
    
    @feedback_history.setter
    def feedback_history(self, value):
        """Set feedback history from list."""
        self.feedback_history_json = json.dumps(value) if value else None
    
    @property
    def tournament_record(self):
        """Get tournament record as dictionary."""
        if self.tournament_record_json:
            return json.loads(self.tournament_record_json)
        return {}
    
    @tournament_record.setter
    def tournament_record(self, value):
        """Set tournament record from dictionary."""
        self.tournament_record_json = json.dumps(value) if value else None
    
    @property
    def agent_contributions(self):
        """Get agent contributions as list."""
        if self.agent_contributions_json:
            return json.loads(self.agent_contributions_json)
        return []
    
    @agent_contributions.setter
    def agent_contributions(self, value):
        """Set agent contributions from list."""
        self.agent_contributions_json = json.dumps(value) if value else None
    
    @property
    def metadata(self):
        """Get metadata as dictionary."""
        if self.metadata_json:
            return json.loads(self.metadata_json)
        return {}
    
    @metadata.setter
    def metadata(self, value):
        """Set metadata from dictionary."""
        self.metadata_json = json.dumps(value) if value else None
    
    def to_dict(self):
        """Convert hypothesis to dictionary."""
        return {
            'id': self.id,
            'hypothesis_id': self.hypothesis_id,
            'session_id': self.session_id,
            'title': self.title,
            'content': self.content,
            'description': self.description,
            'experimental_validation': self.experimental_validation,
            'version': self.version,
            'version_string': self.version_string,
            'hypothesis_type': self.hypothesis_type,
            'hypothesis_number': self.hypothesis_number,
            'generation_strategy': self.generation_strategy,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'generation_timestamp': self.generation_timestamp,
            'hallmarks': self.hallmarks,
            'references': self.references,
            'feedback_history': self.feedback_history,
            'tournament_record': self.tournament_record,
            'agent_contributions': self.agent_contributions,
            'metadata': self.metadata,
            'notes': self.notes,
            'improvements_made': self.improvements_made,
            'user_feedback': self.user_feedback
        }
    
    def from_unified_hypothesis(self, unified_hyp):
        """Populate from UnifiedHypothesis object."""
        self.hypothesis_id = unified_hyp.hypothesis_id
        self.title = unified_hyp.title
        self.content = unified_hyp.content
        self.description = unified_hyp.description
        self.experimental_validation = unified_hyp.experimental_validation
        self.version = unified_hyp.version
        self.version_string = unified_hyp.version_string
        self.hypothesis_type = unified_hyp.hypothesis_type
        self.hypothesis_number = unified_hyp.hypothesis_number
        self.generation_strategy = unified_hyp.generation_strategy
        self.generation_timestamp = unified_hyp.generation_timestamp
        self.notes = unified_hyp.notes
        self.improvements_made = unified_hyp.improvements_made
        self.user_feedback = unified_hyp.user_feedback
        
        # Convert complex objects to JSON
        self.hallmarks = unified_hyp.hallmarks.to_dict() if hasattr(unified_hyp.hallmarks, 'to_dict') else unified_hyp.hallmarks.__dict__
        self.references = [ref.to_dict() if hasattr(ref, 'to_dict') else ref.__dict__ for ref in unified_hyp.references]
        self.feedback_history = [fb.to_dict() if hasattr(fb, 'to_dict') else fb.__dict__ for fb in unified_hyp.feedback_history]
        self.tournament_record = unified_hyp.tournament_record.to_dict() if hasattr(unified_hyp.tournament_record, 'to_dict') else unified_hyp.tournament_record.__dict__
        self.agent_contributions = [ac.to_dict() if hasattr(ac, 'to_dict') else ac.__dict__ for ac in unified_hyp.agent_contributions]
        self.metadata = unified_hyp.metadata
