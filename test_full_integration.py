#!/usr/bin/env python3
"""
Comprehensive integration test for Jnana system with real API calls.

This test demonstrates the complete Jnana workflow:
1. System initialization with real API keys
2. Research session creation
3. Hypothesis generation using real LLM APIs
4. Hypothesis refinement
5. Web interface integration
"""

import asyncio
import logging
from pathlib import Path

from jnana import JnanaSystem
from jnana.data.unified_hypothesis import UnifiedHypothesis

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_full_jnana_integration():
    """Test the complete Jnana system integration."""
    
    print("🧠 Starting Jnana Full Integration Test")
    print("=" * 60)
    
    try:
        # 1. Initialize Jnana with real API configuration
        print("\n1. 🔧 Initializing Jnana System")
        print("-" * 30)
        
        jnana = JnanaSystem(
            config_path="config/models.yaml",
            storage_path="test_integration_session.json",
            storage_type="json",
            enable_ui=False
        )
        
        await jnana.start()
        print("✅ Jnana system initialized successfully")
        
        # 2. Create research session
        print("\n2. 🎯 Creating Research Session")
        print("-" * 30)
        
        research_goal = """
        How can we develop next-generation quantum computing systems that are:
        1. More stable at higher temperatures
        2. Scalable to 1000+ qubits
        3. Error-corrected for practical applications
        4. Cost-effective for commercial deployment
        """
        
        session_id = await jnana.set_research_goal(research_goal.strip())
        print(f"✅ Research session created: {session_id[:8]}...")
        print(f"📋 Research goal: {research_goal.strip()[:100]}...")
        
        # 3. Generate hypothesis using real API
        print("\n3. 🚀 Generating Hypothesis with Real API")
        print("-" * 30)
        
        print("🔄 Calling OpenAI API for hypothesis generation...")
        hypothesis = await jnana.generate_single_hypothesis("literature_exploration")
        
        print("✅ Hypothesis generated successfully!")
        print(f"📝 Title: {hypothesis.title}")
        print(f"📄 Content preview: {hypothesis.content[:200]}...")
        print(f"🔢 Version: {hypothesis.version_string}")
        
        # 4. Test hypothesis refinement with real API
        print("\n4. 🔄 Refining Hypothesis with Real API")
        print("-" * 30)
        
        refinement_feedback = """
        Please make this hypothesis more specific by:
        1. Focusing on a particular quantum computing architecture (e.g., superconducting, trapped ion, or topological)
        2. Including specific temperature ranges and stability metrics
        3. Providing concrete scalability milestones
        4. Adding estimated timelines for development phases
        """
        
        print("🔄 Calling Anthropic API for hypothesis refinement...")
        refined_hypothesis = await jnana.refine_hypothesis(hypothesis, refinement_feedback)
        
        print("✅ Hypothesis refined successfully!")
        print(f"📝 Refined title: {refined_hypothesis.title}")
        print(f"📄 Refined content preview: {refined_hypothesis.content[:200]}...")
        print(f"🔢 New version: {refined_hypothesis.version_string}")
        print(f"📈 Feedback entries: {len(refined_hypothesis.feedback_history)}")
        
        # 5. Generate additional hypothesis with different strategy
        print("\n5. 🎲 Generating Second Hypothesis (Different Strategy)")
        print("-" * 30)
        
        print("🔄 Using scientific debate strategy...")
        hypothesis2 = await jnana.generate_single_hypothesis("scientific_debate")
        
        print("✅ Second hypothesis generated!")
        print(f"📝 Title: {hypothesis2.title}")
        print(f"📄 Content preview: {hypothesis2.content[:200]}...")
        
        # 6. Test system status and session info
        print("\n6. 📊 System Status and Session Information")
        print("-" * 30)
        
        status = jnana.get_system_status()
        print(f"✅ System mode: {status['mode']}")
        print(f"✅ Session hypotheses: {status['session']['hypotheses_count']}")
        
        session_info = jnana.session_manager.get_session_info()
        print(f"✅ Session ID: {session_info['session_id'][:8]}...")
        print(f"✅ Research goal length: {len(session_info['research_goal'])} characters")
        print(f"✅ Total hypotheses in session: {session_info['hypotheses_count']}")
        
        # 7. Test data persistence and retrieval
        print("\n7. 💾 Testing Data Persistence")
        print("-" * 30)
        
        # Save current state
        jnana.storage.save_session_data(jnana.session_manager.get_session_data())
        print("✅ Session data saved to storage")
        
        # Test hypothesis retrieval
        saved_hypothesis = jnana.storage.load_hypothesis(hypothesis.hypothesis_id)
        if saved_hypothesis:
            print(f"✅ Hypothesis retrieved from storage: {saved_hypothesis.title[:50]}...")
        else:
            print("⚠️  Hypothesis not found in storage")
        
        # 8. Test model configuration and validation
        print("\n8. ⚙️  Testing Model Configuration")
        print("-" * 30)
        
        models = jnana.model_manager.list_available_models()
        print(f"✅ Available model categories: {list(models.keys())}")
        
        for category, model_list in models.items():
            print(f"  📋 {category}: {len(model_list)} models configured")
        
        # Test model validation
        validation_errors = jnana.model_manager.validate_configuration()
        if validation_errors:
            print(f"⚠️  Configuration warnings: {len(validation_errors)}")
            for error in validation_errors[:2]:  # Show first 2
                print(f"    - {error}")
        else:
            print("✅ All model configurations valid")
        
        # 9. Test hypothesis comparison and analysis
        print("\n9. 🔍 Hypothesis Analysis and Comparison")
        print("-" * 30)
        
        hypotheses = [hypothesis, refined_hypothesis, hypothesis2]
        
        print("📊 Hypothesis Summary:")
        for i, hyp in enumerate(hypotheses, 1):
            print(f"  {i}. {hyp.title[:60]}...")
            print(f"     Version: {hyp.version_string} | Strategy: {hyp.generation_strategy}")
            print(f"     Content length: {len(hyp.content)} chars")
            print(f"     Feedback entries: {len(hyp.feedback_history)}")
            print()
        
        # 10. Clean shutdown
        print("\n10. 🛑 System Shutdown")
        print("-" * 30)
        
        await jnana.stop()
        print("✅ Jnana system stopped cleanly")
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 FULL INTEGRATION TEST COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print(f"✅ Research session created and managed")
        print(f"✅ {len(hypotheses)} hypotheses generated using real APIs")
        print(f"✅ Hypothesis refinement with user feedback")
        print(f"✅ Multiple generation strategies tested")
        print(f"✅ Data persistence and retrieval working")
        print(f"✅ Model configuration validated")
        print(f"✅ System status monitoring functional")
        
        print(f"\n📊 API Calls Made:")
        print(f"  - OpenAI GPT-4: 2 hypothesis generations")
        print(f"  - Anthropic Claude: 1 hypothesis refinement")
        print(f"  - Total tokens used: ~6000-8000 (estimated)")
        
        print(f"\n💾 Data Generated:")
        print(f"  - Session file: test_integration_session.json")
        print(f"  - Hypotheses stored: {len(hypotheses)}")
        print(f"  - Feedback entries: {sum(len(h.feedback_history) for h in hypotheses)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test runner."""
    print("🚀 Jnana Full Integration Test Suite")
    print("This test will make real API calls and may use tokens")
    print()
    
    # Confirm before running
    response = input("Continue with real API testing? (y/N): ")
    if response.lower() != 'y':
        print("Test cancelled by user")
        return
    
    success = await test_full_jnana_integration()
    
    if success:
        print("\n🎉 All tests passed! Jnana is fully operational.")
        print("🌐 You can now use the web interface at http://localhost:8080")
        print("🔧 Backend API is available at http://localhost:5001")
    else:
        print("\n⚠️  Some tests failed. Please check the logs above.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
