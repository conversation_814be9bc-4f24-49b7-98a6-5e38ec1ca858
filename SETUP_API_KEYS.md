# 🔑 API Keys Setup Guide

This guide explains how to set up API keys for Jnana to work with real LLM providers.

## 🚨 **Security Notice**

**NEVER commit API keys to version control!** All API keys should be stored as environment variables or in a local `.env` file that is excluded from git.

---

## 📋 **Required API Keys**

### **OpenAI API Key**
- **Provider**: OpenAI
- **Models**: GPT-4, GPT-4-turbo, GPT-3.5-turbo
- **Get your key**: https://platform.openai.com/api-keys
- **Environment variable**: `OPENAI_API_KEY`

### **Anthropic API Key**
- **Provider**: Anthropic
- **Models**: Claude-3.5-<PERSON><PERSON>, <PERSON>-3-<PERSON>, <PERSON>-3-<PERSON><PERSON>
- **Get your key**: https://console.anthropic.com/
- **Environment variable**: `ANTHROPIC_API_KEY`

---

## ⚙️ **Setup Methods**

### **Method 1: Environment Variables (Recommended)**

```bash
# Add to your shell profile (~/.bashrc, ~/.zshrc, etc.)
export OPENAI_API_KEY="your_openai_api_key_here"
export ANTHROPIC_API_KEY="your_anthropic_api_key_here"

# Reload your shell or run:
source ~/.bashrc  # or ~/.zshrc
```

### **Method 2: .env File**

```bash
# 1. Copy the example file
cp .env.example .env

# 2. Edit the .env file with your API keys
nano .env  # or use your preferred editor

# 3. Add your keys:
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### **Method 3: Runtime Export**

```bash
# Set for current session only
export OPENAI_API_KEY="your_openai_api_key_here"
export ANTHROPIC_API_KEY="your_anthropic_api_key_here"

# Then run Jnana
python jnana.py
```

---

## 🧪 **Testing Your Setup**

### **1. Test Configuration Loading**
```bash
python -c "
from jnana.core.model_manager import UnifiedModelManager
manager = UnifiedModelManager('config/models.yaml')
errors = manager.validate_configuration()
if not errors:
    print('✅ Configuration valid!')
else:
    print('⚠️ Issues found:', errors)
"
```

### **2. Test Real API Calls**
```bash
python test_api_integration.py
```

### **3. Test Full Integration**
```bash
python test_full_integration.py
```

---

## 🔧 **Configuration File**

The `config/models.yaml` file uses environment variable substitution:

```yaml
default:
  provider: "anthropic"
  model: "claude-3-5-sonnet-20241022"
  api_key: "${ANTHROPIC_API_KEY}"  # ← Environment variable
  temperature: 0.7
  max_tokens: 2048

agents:
  generation:
    provider: "openai"
    model: "gpt-4o"
    api_key: "${OPENAI_API_KEY}"   # ← Environment variable
    temperature: 0.8
    max_tokens: 3072
```

---

## 🌐 **Web Interface Setup**

For the web interface, you can also set API keys in the backend environment:

```bash
# Navigate to web backend
cd jnana-web/backend

# Create .env file
cat > .env << EOF
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here
EOF

# Start the backend
source venv/bin/activate
python run.py
```

---

## 🚨 **Security Best Practices**

### **✅ DO:**
- Use environment variables
- Use `.env` files (excluded from git)
- Rotate API keys regularly
- Use different keys for development/production
- Monitor API usage and costs

### **❌ DON'T:**
- Commit API keys to version control
- Share API keys in chat/email
- Use production keys for testing
- Store keys in plain text files
- Use overly permissive API keys

---

## 🔍 **Troubleshooting**

### **"API key not found" Error**
```bash
# Check if environment variable is set
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY

# If empty, set them:
export OPENAI_API_KEY="your_key_here"
```

### **"Invalid API key" Error**
- Verify the key is correct (no extra spaces)
- Check if the key has expired
- Ensure you have sufficient credits/quota
- Verify the key has the right permissions

### **"Configuration validation failed"**
```bash
# Check the config file syntax
python -c "
import yaml
with open('config/models.yaml') as f:
    config = yaml.safe_load(f)
    print('✅ YAML syntax is valid')
"
```

---

## 💰 **Cost Management**

### **Monitor Usage**
- OpenAI: https://platform.openai.com/usage
- Anthropic: https://console.anthropic.com/usage

### **Set Limits**
- Configure usage limits in provider dashboards
- Monitor token consumption in Jnana logs
- Use cheaper models for testing (e.g., GPT-3.5-turbo)

### **Optimize Costs**
- Use appropriate temperature settings
- Set reasonable max_tokens limits
- Cache results when possible
- Use local models for development

---

## 📞 **Support**

If you encounter issues:

1. **Check the logs**: Look for error messages in the console output
2. **Validate configuration**: Run the test scripts above
3. **Check API status**: Verify provider service status
4. **Review documentation**: Check provider API documentation
5. **Create an issue**: Report bugs on the GitHub repository

---

## 🎉 **Ready to Go!**

Once your API keys are set up:

```bash
# Test the system
python test_full_integration.py

# Start the CLI interface
python jnana.py

# Start the web interface
cd jnana-web && ./start.sh
```

**Happy researching with Jnana! 🧠✨**
