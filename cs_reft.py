"""
CS-ReFT: Compositional Subspace Representation Fine-tuning

This module implements the CS-ReFT approach for adapting language models
to multiple tasks using compositional subspace representations.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Optional, Union, Any

class CSReFT(nn.Module):
    """
    CS-ReFT adapter module.
    
    This module implements the CS-ReFT approach for adapting language models
    to multiple tasks using compositional subspace representations.
    """
    
    def __init__(
        self,
        hidden_size: int,
        subspace_dim: int,
        task_ids: List[str],
        target_layers: List[int]
    ):
        """
        Initialize the CS-ReFT adapter.
        
        Args:
            hidden_size: Hidden size of the base model
            subspace_dim: Dimension of task-specific subspaces
            task_ids: List of task identifiers
            target_layers: Indices of layers to apply adaptation to
        """
        super().__init__()
        
        self.hidden_size = hidden_size
        self.subspace_dim = subspace_dim
        self.task_ids = task_ids
        self.target_layers = target_layers
        
        # Create task-specific projections and reconstructions
        self.task_projections = nn.ModuleDict({
            task_id: nn.Linear(hidden_size, subspace_dim)
            for task_id in task_ids
        })
        
        self.task_reconstructions = nn.ModuleDict({
            task_id: nn.Linear(subspace_dim, hidden_size)
            for task_id in task_ids
        })
        
        # Router for multi-task
        self.router = nn.Linear(hidden_size, len(task_ids))
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights for the adapter."""
        # Initialize projections and reconstructions
        for task_id in self.task_ids:
            # Initialize projection
            nn.init.normal_(self.task_projections[task_id].weight, std=0.02)
            nn.init.zeros_(self.task_projections[task_id].bias)
            
            # Initialize reconstruction
            nn.init.normal_(self.task_reconstructions[task_id].weight, std=0.02)
            nn.init.zeros_(self.task_reconstructions[task_id].bias)
        
        # Initialize router
        nn.init.normal_(self.router.weight, std=0.02)
        nn.init.zeros_(self.router.bias)
    
    def forward(
        self,
        hidden_states: torch.Tensor,
        layer_idx: int,
        task_id: Optional[str] = None
    ) -> torch.Tensor:
        """
        Apply CS-ReFT transformation to hidden states.
        
        Args:
            hidden_states: Tensor of shape [batch_size, seq_len, hidden_size]
            layer_idx: Current layer index
            task_id: Optional task identifier. If provided, only that task's
                   transformation is applied. If None, routing is used.
            
        Returns:
            Transformed hidden states
        """
        # Check if this layer should be adapted
        if layer_idx not in self.target_layers:
            return hidden_states
        
        # If task_id is provided, use that specific subspace
        if task_id is not None and task_id in self.task_ids:
            # Project to task-specific subspace
            subspace_repr = self.task_projections[task_id](hidden_states)
            
            # Apply non-linearity
            subspace_repr = F.gelu(subspace_repr)
            
            # Reconstruct from subspace
            delta = self.task_reconstructions[task_id](subspace_repr)
            
            # Add residual connection
            return hidden_states + delta
        
        # If no task_id is provided, use router to determine task weights
        else:
            # Get average hidden state for routing
            avg_hidden = hidden_states.mean(dim=1)
            
            # Compute task weights
            task_weights = F.softmax(self.router(avg_hidden), dim=-1)
            
            # Initialize delta
            delta = torch.zeros_like(hidden_states)
            
            # Apply each task transformation with its weight
            for i, task_id in enumerate(self.task_ids):
                # Get task weight for this batch
                weight = task_weights[:, i].unsqueeze(1).unsqueeze(2)
                
                # Project to task-specific subspace
                subspace_repr = self.task_projections[task_id](hidden_states)
                
                # Apply non-linearity
                subspace_repr = F.gelu(subspace_repr)
                
                # Reconstruct from subspace
                task_delta = self.task_reconstructions[task_id](subspace_repr)
                
                # Add weighted task delta
                delta = delta + weight * task_delta
            
            # Add residual connection
            return hidden_states + delta
    
    def get_parameter_count(self) -> int:
        """Return the number of parameters in the adapter."""
        return sum(p.numel() for p in self.parameters())
