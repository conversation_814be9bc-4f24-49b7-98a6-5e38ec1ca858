"""
Test script for CS-ReFT integration with CoScientist.
This script tests the basic functionality without requiring a full training run.
"""

import os
import logging
import torch
from pathlib import Path

from coscientist import CoScientist
from multi_llm_config import LLMConfig, AgentLLMConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define the adapter class at module level so it can be pickled
class DummyCSReFTAdapter(torch.nn.Module):
    def __init__(self, task_ids, target_layers=None, hidden_size=4096, subspace_dim=32):
        super().__init__()
        self.task_ids = task_ids
        self.target_layers = target_layers or [-4, -3, -2, -1]
        self.hidden_size = hidden_size
        self.subspace_dim = subspace_dim
        
        # Create dummy parameters for each task
        self.task_projections = torch.nn.ModuleDict({
            task_id: torch.nn.Linear(hidden_size, subspace_dim)
            for task_id in task_ids
        })
        
        self.task_reconstructions = torch.nn.ModuleDict({
            task_id: torch.nn.Linear(subspace_dim, hidden_size)
            for task_id in task_ids
        })
        
        # Router for multi-task
        self.router = torch.nn.Linear(hidden_size, len(task_ids))
    
    def forward(self, hidden_states, layer_idx, task_id=None):
        """Dummy forward pass that would normally transform hidden states"""
        # In a real implementation, this would apply the task-specific transformation
        return hidden_states  # Just return input for testing
    
    def get_parameter_count(self):
        """Return the number of parameters in the adapter"""
        return sum(p.numel() for p in self.parameters())

def create_dummy_adapter():
    """
    Create a dummy CS-ReFT adapter for testing purposes.
    This simulates what would normally be created through training.
    """
    print("Creating dummy CS-ReFT adapter for testing...")
    
    # Create output directory if it doesn't exist
    output_dir = Path("./test_adapted_models/coscientist_adapter")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Define task IDs
    task_ids = ["generation", "reflection", "ranking", "evolution", "meta_review"]
    
    # Create the dummy adapter using the class defined at module level
    adapter = DummyCSReFTAdapter(task_ids)
    
    # Save the adapter
    adapter_path = output_dir / "adapter.pt"
    torch.save(adapter, adapter_path)
    
    print(f"Saved dummy adapter to {adapter_path}")
    print(f"Adapter has {adapter.get_parameter_count()} parameters")
    
    return str(adapter_path)

def test_cs_reft_integration():
    """
    Test the integration of CS-ReFT with CoScientist.
    This is a minimal test that verifies the configuration works.
    """
    print("Testing CS-ReFT integration with CoScientist...")
    
    # Create a dummy adapter for testing
    adapter_path = create_dummy_adapter()
    
    # Configure with the dummy adapter
    llm_config = AgentLLMConfig(
        # For testing, we'll use a cloud provider since we don't actually have
        # a local model implementation ready
        default=LLMConfig(
            provider="anthropic",  # Using Anthropic for testing
            model="claude-3-5-haiku-20241022",
            api_key=os.environ.get("ANTHROPIC_API_KEY", "dummy_key_for_testing"),
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": None  # Use router for multi-task
            }
        ),
        
        # Task-specific configurations
        generation=LLMConfig(
            provider="anthropic",
            model="claude-3-5-haiku-20241022",
            api_key=os.environ.get("ANTHROPIC_API_KEY", "dummy_key_for_testing"),
            model_adapter={
                "type": "cs_reft",
                "path": adapter_path,
                "task_id": "generation"
            }
        )
    )
    
    # Create CoScientist with our test configuration
    try:
        coscientist = CoScientist(
            llm_config=llm_config,
            storage_path="./test_cs_reft_results.json",
            max_workers=2
        )
        print("✅ Successfully created CoScientist with CS-ReFT configuration")
        
        # Test setting a research goal (this should work even without API calls)
        research_goal = "Suggest novel biologics for targeting ALKBH1 in cancer treatment."
        
        # If we have a valid API key, try setting the research goal
        if os.environ.get("ANTHROPIC_API_KEY"):
            try:
                print("\nSetting research goal...")
                coscientist.set_research_goal(research_goal)
                print("✅ Successfully set research goal")
                
                # Start the system
                print("\nStarting CoScientist...")
                coscientist.start()
                print("✅ Successfully started CoScientist")
                
                # Generate a single hypothesis to test the integration
                print("\nGenerating a test hypothesis...")
                coscientist.generate_hypotheses(count=1)
                coscientist.wait_for_completion(timeout=60)
                print("✅ Successfully generated test hypothesis")
                
            except Exception as e:
                print(f"❌ Error during API test: {e}")
            finally:
                coscientist.stop()
        else:
            print("\nSkipping API tests (no API key provided)")
            print("To run a full test, set the ANTHROPIC_API_KEY environment variable")
    
    except Exception as e:
        print(f"❌ Error creating CoScientist: {e}")
        raise

if __name__ == "__main__":
    test_cs_reft_integration()
