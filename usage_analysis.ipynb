{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coscientist model usages at 336/400 ranking comparisons (84.0%)\n"]}], "source": ["import json\n", "from pprint import pprint\n", "\n", "with open(\"./model_usages.json\", \"r\") as f:\n", "    model_usages = json.load(f)\n", "\n", "print(f\"Coscientist model usages at 336/400 ranking comparisons ({336./400 * 100}%)\")\n", "\n", "total_calls = model_usages[\"total_calls\"]\n", "total_prompt_tokens = model_usages[\"total_prompt_tokens\"]\n", "total_completion_tokens = model_usages[\"total_completion_tokens\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["total_calls[\"total\"] = sum(list(total_calls.values()))\n", "total_prompt_tokens[\"total\"] = sum(list(total_prompt_tokens.values()))\n", "total_completion_tokens[\"total\"] = sum(list(total_completion_tokens.values()))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'evolution-0': 195,\n", " 'generation-0': 14,\n", " 'generation-1': 18,\n", " 'generation-2': 18,\n", " 'generation-3': 18,\n", " 'generation-4': 0,\n", " 'meta-review-0': 0,\n", " 'proximity-0': 0,\n", " 'ranking-0': 336,\n", " 'reflection-0': 11586,\n", " 'reflection-1': 11418,\n", " 'supervisor': 1,\n", " 'total': 23604}\n"]}], "source": ["pprint(total_calls)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'evolution-0': 632566,\n", " 'generation-0': 6976,\n", " 'generation-1': 8691,\n", " 'generation-2': 8954,\n", " 'generation-3': 8801,\n", " 'generation-4': 0,\n", " 'meta-review-0': 0,\n", " 'proximity-0': 0,\n", " 'ranking-0': 226645,\n", " 'reflection-0': 8190999,\n", " 'reflection-1': 8072973,\n", " 'supervisor': 357,\n", " 'total': 17156962}\n"]}], "source": ["pprint(total_prompt_tokens)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'evolution-0': 115711,\n", " 'generation-0': 6548,\n", " 'generation-1': 8484,\n", " 'generation-2': 8429,\n", " 'generation-3': 8272,\n", " 'generation-4': 0,\n", " 'meta-review-0': 0,\n", " 'proximity-0': 0,\n", " 'ranking-0': 145640,\n", " 'reflection-0': 7480714,\n", " 'reflection-1': 7392747,\n", " 'supervisor': 152,\n", " 'total': 15166697}\n"]}], "source": ["pprint(total_completion_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "r2.4", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}