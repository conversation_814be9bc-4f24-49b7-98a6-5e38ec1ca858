<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jnana - Live Web Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', system-ui, sans-serif; }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
            <!-- Header -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <i data-lucide="brain" class="h-6 w-6 text-blue-600"></i>
                    <h1 class="text-lg font-semibold text-gray-900">Jnana Live</h1>
                </div>
                <p class="text-sm text-gray-500 mt-1">AI Co-Scientist - Real API</p>
            </div>

            <!-- Connection Status -->
            <div id="connection-status" class="px-4 py-2 border-b border-gray-200 bg-yellow-50">
                <div class="flex items-center space-x-2">
                    <i data-lucide="wifi-off" class="h-4 w-4 text-yellow-600"></i>
                    <span class="text-xs font-medium text-yellow-700">Connecting...</span>
                </div>
            </div>

            <!-- Sessions -->
            <div class="flex-1 flex flex-col min-h-0">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-sm font-medium text-gray-900">Research Sessions</h2>
                        <button onclick="createSession()" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700">
                            <i data-lucide="plus" class="h-3 w-3 mr-1"></i>
                            New
                        </button>
                    </div>
                </div>

                <div class="flex-1 overflow-y-auto" id="sessions-list">
                    <div class="p-4 text-center text-gray-500">
                        <i data-lucide="loader" class="h-6 w-6 mx-auto mb-2 loading"></i>
                        <p class="text-sm">Loading sessions...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-w-0">
            <!-- Header -->
            <header class="bg-white border-b border-gray-200 px-4 py-3">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">Welcome to Jnana Live</h1>
                        <p class="text-sm text-gray-500">Real-time AI-assisted research with live API integration</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="testAPI()" class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                            Test API
                        </button>
                        <button onclick="generateHypothesis()" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                            Generate Hypothesis
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-hidden p-6">
                <div class="max-w-4xl mx-auto">
                    <!-- Status Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="server" class="h-6 w-6 text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-sm font-medium text-gray-900">Backend Status</h3>
                                    <p id="backend-status" class="text-sm text-gray-500">Checking...</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i data-lucide="zap" class="h-6 w-6 text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-sm font-medium text-gray-900">API Status</h3>
                                    <p id="api-status" class="text-sm text-gray-500">Ready</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i data-lucide="activity" class="h-6 w-6 text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-sm font-medium text-gray-900">Sessions</h3>
                                    <p id="session-count" class="text-sm text-gray-500">0 active</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Interface -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Research Interface</h2>
                            <p class="text-gray-600 mt-1">Create sessions and generate hypotheses using real AI models</p>
                        </div>

                        <div class="p-6">
                            <!-- Create Session Form -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Create Research Session</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Research Goal</label>
                                        <textarea id="research-goal" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Describe your research question or goal..."></textarea>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Mode</label>
                                        <select id="session-mode" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="interactive">Interactive</option>
                                            <option value="batch">Batch</option>
                                            <option value="hybrid">Hybrid</option>
                                        </select>
                                    </div>
                                    <button onclick="createSessionFromForm()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                        Create Session
                                    </button>
                                </div>
                            </div>

                            <!-- Output Area -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Output</h3>
                                <div id="output" class="bg-gray-50 rounded-lg p-4 min-h-32 font-mono text-sm">
                                    <p class="text-gray-500">Ready for commands...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // API base URL
        const API_BASE = 'http://localhost:5001/api';
        
        // WebSocket connection
        let socket = null;

        // Initialize the application
        async function init() {
            await checkBackendStatus();
            await loadSessions();
            connectWebSocket();
        }

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('backend-status').textContent = 'Connected';
                    document.getElementById('connection-status').innerHTML = `
                        <div class="flex items-center space-x-2">
                            <i data-lucide="wifi" class="h-4 w-4 text-green-600"></i>
                            <span class="text-xs font-medium text-green-700">Connected</span>
                        </div>
                    `;
                    document.getElementById('connection-status').className = 'px-4 py-2 border-b border-gray-200 bg-green-50';
                    lucide.createIcons();
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = 'Disconnected';
                log('❌ Backend connection failed: ' + error.message);
            }
        }

        // Load sessions
        async function loadSessions() {
            try {
                const response = await fetch(`${API_BASE}/sessions`);
                const data = await response.json();
                
                const sessionsList = document.getElementById('sessions-list');
                if (data.sessions && data.sessions.length > 0) {
                    sessionsList.innerHTML = data.sessions.map(session => `
                        <div class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
                            <h3 class="text-sm font-medium text-gray-900 truncate">${session.research_goal.substring(0, 50)}...</h3>
                            <div class="flex items-center space-x-3 mt-1">
                                <div class="flex items-center text-xs text-gray-500">
                                    <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
                                    ${new Date(session.created_at).toLocaleDateString()}
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    ${session.mode}
                                </span>
                            </div>
                        </div>
                    `).join('');
                    document.getElementById('session-count').textContent = `${data.sessions.length} active`;
                } else {
                    sessionsList.innerHTML = `
                        <div class="p-4 text-center text-gray-500">
                            <i data-lucide="folder" class="h-6 w-6 mx-auto mb-2"></i>
                            <p class="text-sm">No sessions yet</p>
                        </div>
                    `;
                }
                lucide.createIcons();
            } catch (error) {
                log('❌ Failed to load sessions: ' + error.message);
            }
        }

        // Connect WebSocket
        function connectWebSocket() {
            try {
                socket = io('http://localhost:5001/jnana');
                
                socket.on('connect', () => {
                    log('🔌 WebSocket connected');
                });

                socket.on('session_created', (data) => {
                    log('✅ Session created: ' + data.session_id);
                    loadSessions();
                });

                socket.on('hypothesis_generated', (data) => {
                    log('🧠 Hypothesis generated: ' + data.hypothesis.title);
                });

            } catch (error) {
                log('❌ WebSocket connection failed: ' + error.message);
            }
        }

        // Test API connection
        async function testAPI() {
            log('🧪 Testing API connection...');
            await checkBackendStatus();
            log('✅ API test completed');
        }

        // Create session from form
        async function createSessionFromForm() {
            const goal = document.getElementById('research-goal').value;
            const mode = document.getElementById('session-mode').value;
            
            if (!goal.trim()) {
                log('❌ Please enter a research goal');
                return;
            }

            await createSession(goal, mode);
        }

        // Create session
        async function createSession(goal = 'Test research session', mode = 'interactive') {
            try {
                log('🔄 Creating session...');
                
                const response = await fetch(`${API_BASE}/sessions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        research_goal: goal,
                        mode: mode
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Session created: ${data.session_id}`);
                    log(`📋 Goal: ${data.research_goal}`);
                    document.getElementById('research-goal').value = '';
                    await loadSessions();
                } else {
                    log(`❌ Failed to create session: ${data.error}`);
                }
            } catch (error) {
                log('❌ Error creating session: ' + error.message);
            }
        }

        // Generate hypothesis
        async function generateHypothesis() {
            log('🧠 This feature requires a session. Create a session first, then implement hypothesis generation.');
        }

        // Log function
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
