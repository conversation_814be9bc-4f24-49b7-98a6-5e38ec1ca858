# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.json
*.log

# Jnana specific
sessions/
logs/

# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
config/models.yaml.local
**/instance/
**/*.key
**/*.pem

# API keys and sensitive data
**/api_keys.py
**/secrets.py
**/credentials.json

# Web interface
jnana-web/backend/instance/
jnana-web/frontend/node_modules/
jnana-web/frontend/build/
jnana-web/frontend/.env*
